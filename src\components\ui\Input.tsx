import React, { useState } from 'react';
import {
  View,
  TextInput,
  Text,
  TouchableOpacity,
  TextInputProps,
  ViewStyle,
  TextStyle,
} from 'react-native';
import { Ionicons } from '@expo/vector-icons';

interface InputProps extends TextInputProps {
  label?: string;
  error?: string;
  hint?: string;
  leftIcon?: keyof typeof Ionicons.glyphMap;
  rightIcon?: keyof typeof Ionicons.glyphMap | React.ReactNode;
  onRightIconPress?: () => void;
  variant?: 'default' | 'filled' | 'outline';
  size?: 'sm' | 'md' | 'lg';
  required?: boolean;
  disabled?: boolean;
  style?: ViewStyle;
  value?: string;
  onChangeText?: (text: string) => void;
  placeholder?: string;
  keyboardType?: string;
  autoCapitalize?: string;
  autoCorrect?: boolean;
  secureTextEntry?: boolean;
}

const Input: React.FC<InputProps> = ({
  label,
  error,
  hint,
  leftIcon,
  rightIcon,
  onRightIconPress,
  variant = 'outline',
  size = 'md',
  required = false,
  disabled = false,
  style,
  ...props
}) => {
  const [isFocused, setIsFocused] = useState(false);

  const getContainerStyles = (): ViewStyle => {
    return {
      marginBottom: 16,
    };
  };

  const getLabelStyles = (): TextStyle => {
    return {
      fontSize: 14,
      fontWeight: '500',
      color: '#374151',
      marginBottom: 6,
    };
  };

  const getInputContainerStyles = (): ViewStyle => {
    const baseStyles: ViewStyle = {
      flexDirection: 'row',
      alignItems: 'center',
      borderRadius: 8,
    };

    // Size styles
    const sizeStyles: Record<string, ViewStyle> = {
      sm: { minHeight: 36, paddingHorizontal: 12 },
      md: { minHeight: 44, paddingHorizontal: 16 },
      lg: { minHeight: 52, paddingHorizontal: 20 },
    };

    // Variant styles
    const variantStyles: Record<string, ViewStyle> = {
      default: {
        backgroundColor: '#f9fafb',
        borderWidth: 0,
      },
      filled: {
        backgroundColor: '#f3f4f6',
        borderWidth: 0,
      },
      outline: {
        backgroundColor: '#ffffff',
        borderWidth: 1,
        borderColor: error ? '#ef4444' : isFocused ? '#ef4444' : '#d1d5db',
      },
    };

    return {
      ...baseStyles,
      ...sizeStyles[size],
      ...variantStyles[variant],
      opacity: disabled ? 0.6 : 1,
    };
  };

  const getInputStyles = (): TextStyle => {
    const sizeStyles: Record<string, TextStyle> = {
      sm: { fontSize: 14 },
      md: { fontSize: 16 },
      lg: { fontSize: 18 },
    };

    return {
      flex: 1,
      color: disabled ? '#9ca3af' : '#111827',
      ...sizeStyles[size],
    };
  };

  const getIconSize = (): number => {
    const sizes = { sm: 16, md: 20, lg: 24 };
    return sizes[size];
  };

  const getIconColor = (): string => {
    if (disabled) return '#9ca3af';
    if (error) return '#ef4444';
    if (isFocused) return '#ef4444';
    return '#6b7280';
  };

  return (
    <View style={getContainerStyles()}>
      {label && (
        <Text style={getLabelStyles()}>
          {label}
          {required && <Text style={{ color: '#ef4444' }}> *</Text>}
        </Text>
      )}
      
      <View style={getInputContainerStyles()}>
        {leftIcon && (
          <Ionicons
            name={leftIcon}
            size={getIconSize()}
            color={getIconColor()}
            style={{ marginRight: 12 }}
          />
        )}
        
        <TextInput
          style={[getInputStyles(), style]}
          onFocus={() => setIsFocused(true)}
          onBlur={() => setIsFocused(false)}
          editable={!disabled}
          placeholderTextColor="#9ca3af"
          {...props}
        />
        
        {rightIcon && (
          <TouchableOpacity
            onPress={onRightIconPress}
            disabled={!onRightIconPress || disabled}
            style={{ marginLeft: 12 }}
          >
            {typeof rightIcon === 'string' ? (
              <Ionicons
                name={rightIcon as keyof typeof Ionicons.glyphMap}
                size={getIconSize()}
                color={getIconColor()}
              />
            ) : (
              rightIcon
            )}
          </TouchableOpacity>
        )}
      </View>
      
      {error && (
        <Text style={{
          fontSize: 12,
          color: '#ef4444',
          marginTop: 4,
        }}>
          {error}
        </Text>
      )}
      
      {hint && !error && (
        <Text style={{
          fontSize: 12,
          color: '#6b7280',
          marginTop: 4,
        }}>
          {hint}
        </Text>
      )}
    </View>
  );
};

export default Input;
