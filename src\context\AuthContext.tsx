import React, { createContext, useContext, useReducer, useEffect } from 'react';
import AsyncStorage from '@react-native-async-storage/async-storage';
import {
  AuthState,
  AuthContextType,
  LoginCredentials,
  PhoneLoginCredentials,
  AuthUser,
  AuthTokens,
  RegistrationData,
} from '../types/auth';
import { mockAuthService } from '../services/auth/mockAuthService';
import AuthService, { LoginResponse } from '../services/api/authService';
import DocumentService from '../services/api/documentService';
import { STORAGE_KEYS, setAuthToken, clearAuthToken } from '../services/api/apiConfig';

// Initial state
const initialState: AuthState = {
  user: null,
  tokens: null,
  isLoading: true,
  isAuthenticated: false,
  error: null,
};

// Action types
type AuthAction =
  | { type: 'SET_LOADING'; payload: boolean }
  | { type: 'SET_ERROR'; payload: string | null }
  | { type: 'LOGIN_SUCCESS'; payload: { user: AuthUser; tokens: AuthTokens } }
  | { type: 'LOGOUT' }
  | { type: 'REFRESH_TOKEN_SUCCESS'; payload: AuthTokens }
  | { type: 'UPDATE_USER'; payload: Partial<AuthUser> }
  | { type: 'UPDATE_VERIFICATION_STATUS'; payload: { verificationStatus: string; documentStatus: any; canGoOnline: boolean } }
  | { type: 'CLEAR_ERROR' };

// Reducer
const authReducer = (state: AuthState, action: AuthAction): AuthState => {
  switch (action.type) {
    case 'SET_LOADING':
      return {
        ...state,
        isLoading: action.payload,
      };
    case 'SET_ERROR':
      return {
        ...state,
        error: action.payload,
        isLoading: false,
      };
    case 'LOGIN_SUCCESS':
      return {
        ...state,
        user: action.payload.user,
        tokens: action.payload.tokens,
        isAuthenticated: true,
        isLoading: false,
        error: null,
      };
    case 'LOGOUT':
      return {
        ...state,
        user: null,
        tokens: null,
        isAuthenticated: false,
        isLoading: false,
        error: null,
      };
    case 'REFRESH_TOKEN_SUCCESS':
      return {
        ...state,
        tokens: action.payload,
        error: null,
      };
    case 'UPDATE_USER':
      return {
        ...state,
        user: state.user ? { ...state.user, ...action.payload } : null,
      };
    case 'UPDATE_VERIFICATION_STATUS':
      return {
        ...state,
        user: state.user ? {
          ...state.user,
          verificationStatus: action.payload.verificationStatus,
          documentStatus: action.payload.documentStatus,
          canGoOnline: action.payload.canGoOnline,
        } : null,
      };
    case 'CLEAR_ERROR':
      return {
        ...state,
        error: null,
      };
    default:
      return state;
  }
};

// Create context
const AuthContext = createContext<AuthContextType | undefined>(undefined);

// Provider component
interface AuthProviderProps {
  children: React.ReactNode;
}

export const AuthProvider: React.FC<AuthProviderProps> = ({ children }) => {
  const [state, dispatch] = useReducer(authReducer, initialState);

  // Helper function to map API user to AuthUser
  const mapApiUserToAuthUser = (apiUser: LoginResponse['user']): AuthUser => ({
    id: apiUser.id,
    email: apiUser.email,
    firstName: apiUser.firstName,
    lastName: apiUser.lastName,
    phone: apiUser.phone,
    isVerified: apiUser.isVerified,
    verificationStatus: apiUser.verificationStatus,
    documentStatus: apiUser.documentStatus,
    profile: apiUser.profile,
    vehicle: apiUser.vehicle,
    bankInfo: apiUser.bankInfo,
    isDemoAccount: apiUser.isDemoAccount,
    canGoOnline: apiUser.verificationStatus === 'verified' || apiUser.isDemoAccount,
    createdAt: apiUser.createdAt,
    updatedAt: apiUser.updatedAt,
  });

  // Helper function to clear auth data
  const clearAuthData = async () => {
    await AsyncStorage.multiRemove([
      STORAGE_KEYS.ACCESS_TOKEN,
      STORAGE_KEYS.REFRESH_TOKEN,
      STORAGE_KEYS.USER_DATA,
    ]);
    await clearAuthToken();
  };

  // Check authentication status on app start
  useEffect(() => {
    checkAuthStatus();
  }, []);

  const checkAuthStatus = async () => {
    try {
      dispatch({ type: 'SET_LOADING', payload: true });

      // Check if we have stored tokens
      const storedToken = await AsyncStorage.getItem(STORAGE_KEYS.ACCESS_TOKEN);
      const storedUserData = await AsyncStorage.getItem(STORAGE_KEYS.USER_DATA);

      if (storedToken && storedUserData) {
        try {
          // Verify token with API
          const response = await AuthService.getCurrentUser();

          if (response.success) {
            const user = response.data;
            const tokens = {
              accessToken: storedToken,
              refreshToken: await AsyncStorage.getItem(STORAGE_KEYS.REFRESH_TOKEN) || '',
              expiresIn: 3600, // Default expiry
            };

            dispatch({
              type: 'LOGIN_SUCCESS',
              payload: { user: mapApiUserToAuthUser(user), tokens },
            });
          } else {
            // Token invalid, clear storage
            await clearAuthData();
            dispatch({ type: 'LOGOUT' });
          }
        } catch (error) {
          // API call failed, try mock service for demo accounts
          const isAuth = await mockAuthService.isAuthenticated();
          if (isAuth) {
            const user = await mockAuthService.getUserData();
            const tokens = await mockAuthService.getTokens();

            if (user && tokens) {
              dispatch({
                type: 'LOGIN_SUCCESS',
                payload: { user, tokens },
              });
            } else {
              dispatch({ type: 'LOGOUT' });
            }
          } else {
            dispatch({ type: 'LOGOUT' });
          }
        }
      } else {
        dispatch({ type: 'LOGOUT' });
      }
    } catch (error) {
      console.error('Auth check error:', error);
      dispatch({ type: 'LOGOUT' });
    } finally {
      dispatch({ type: 'SET_LOADING', payload: false });
    }
  };

  const login = async (credentials: LoginCredentials) => {
    try {
      dispatch({ type: 'SET_LOADING', payload: true });
      dispatch({ type: 'CLEAR_ERROR' });

      // Check if it's a demo account first
      if (credentials.email.includes('demo') || credentials.email.includes('test')) {
        const response = await mockAuthService.login(credentials);

        dispatch({
          type: 'LOGIN_SUCCESS',
          payload: {
            user: response.user,
            tokens: response.tokens,
          },
        });
        return;
      }

      // Use API for real accounts
      const deviceId = await AsyncStorage.getItem(STORAGE_KEYS.DEVICE_ID) ||
                      Math.random().toString(36).substring(7);

      if (!await AsyncStorage.getItem(STORAGE_KEYS.DEVICE_ID)) {
        await AsyncStorage.setItem(STORAGE_KEYS.DEVICE_ID, deviceId);
      }

      const response = await AuthService.login({
        email: credentials.email,
        password: credentials.password,
        deviceId,
      });

      if (response.success) {
        const { user, tokens } = response.data;

        // Store tokens and user data
        await AsyncStorage.setItem(STORAGE_KEYS.ACCESS_TOKEN, tokens.accessToken);
        await AsyncStorage.setItem(STORAGE_KEYS.REFRESH_TOKEN, tokens.refreshToken);
        await AsyncStorage.setItem(STORAGE_KEYS.USER_DATA, JSON.stringify(user));
        await setAuthToken(tokens.accessToken);

        dispatch({
          type: 'LOGIN_SUCCESS',
          payload: {
            user: mapApiUserToAuthUser(user),
            tokens,
          },
        });
      } else {
        throw new Error(response.message || 'Login failed');
      }
    } catch (error: any) {
      dispatch({
        type: 'SET_ERROR',
        payload: error.message || 'Login failed',
      });
      throw error;
    }
  };

  const loginWithPhone = async (credentials: PhoneLoginCredentials) => {
    try {
      dispatch({ type: 'SET_LOADING', payload: true });
      dispatch({ type: 'CLEAR_ERROR' });
      
      const response = await mockAuthService.loginWithPhone(credentials);
      
      dispatch({
        type: 'LOGIN_SUCCESS',
        payload: {
          user: response.user,
          tokens: response.tokens,
        },
      });
    } catch (error: any) {
      dispatch({
        type: 'SET_ERROR',
        payload: error.message || 'Phone login failed',
      });
      throw error;
    }
  };

  const register = async (userData: RegistrationData) => {
    try {
      dispatch({ type: 'SET_LOADING', payload: true });
      dispatch({ type: 'CLEAR_ERROR' });

      const deviceId = await AsyncStorage.getItem(STORAGE_KEYS.DEVICE_ID) ||
                      Math.random().toString(36).substring(7);

      if (!await AsyncStorage.getItem(STORAGE_KEYS.DEVICE_ID)) {
        await AsyncStorage.setItem(STORAGE_KEYS.DEVICE_ID, deviceId);
      }

      const response = await AuthService.register({
        ...userData,
        deviceId,
      });

      if (response.success) {
        return response.data;
      } else {
        throw new Error(response.message || 'Registration failed');
      }
    } catch (error: any) {
      dispatch({
        type: 'SET_ERROR',
        payload: error.message || 'Registration failed',
      });
      throw error;
    } finally {
      dispatch({ type: 'SET_LOADING', payload: false });
    }
  };

  const logout = async () => {
    try {
      dispatch({ type: 'SET_LOADING', payload: true });

      // Try API logout first
      try {
        if (state.user && !state.user.isDemoAccount) {
          await AuthService.logout();
        } else {
          await mockAuthService.logout();
        }
      } catch (apiError) {
        console.warn('API logout failed:', apiError);
      }

      // Clear local storage
      await clearAuthData();

      dispatch({ type: 'LOGOUT' });
    } catch (error: any) {
      console.error('Logout error:', error);
      // Force logout even if API call fails
      await clearAuthData();
      dispatch({ type: 'LOGOUT' });
    }
  };

  const refreshToken = async () => {
    try {
      const newTokens = await mockAuthService.refreshToken();
      
      dispatch({
        type: 'REFRESH_TOKEN_SUCCESS',
        payload: newTokens,
      });
    } catch (error: any) {
      console.error('Token refresh error:', error);
      dispatch({ type: 'LOGOUT' });
      throw error;
    }
  };

  const clearError = () => {
    dispatch({ type: 'CLEAR_ERROR' });
  };

  // Check verification status
  const checkVerificationStatus = async () => {
    try {
      if (!state.user || state.user.isDemoAccount) {
        return;
      }

      const response = await DocumentService.getVerificationStatus();

      if (response.success) {
        const { overall, canGoOnline } = response.data;

        dispatch({
          type: 'UPDATE_VERIFICATION_STATUS',
          payload: {
            verificationStatus: overall,
            documentStatus: response.data,
            canGoOnline,
          },
        });
      }
    } catch (error) {
      console.error('Verification status check error:', error);
    }
  };

  // Check if user can go online
  const canGoOnline = (): boolean => {
    if (!state.user) return false;

    // Demo accounts can always go online
    if (state.user.isDemoAccount) return true;

    // Real accounts need verification
    return state.user.verificationStatus === 'verified';
  };

  // Upload document
  const uploadDocument = async (documentType: string, file: any, metadata?: any) => {
    try {
      if (!state.user || state.user.isDemoAccount) {
        throw new Error('Document upload not available for demo accounts');
      }

      const response = await DocumentService.uploadDocument({
        documentType,
        file,
        metadata,
      });

      if (response.success) {
        // Refresh verification status after upload
        await checkVerificationStatus();
        return response.data;
      } else {
        throw new Error(response.message || 'Document upload failed');
      }
    } catch (error: any) {
      throw error;
    }
  };

  const contextValue: AuthContextType = {
    state,
    login,
    loginWithPhone,
    logout,
    refreshToken,
    clearError,
    register,
    checkVerificationStatus,
    canGoOnline,
    uploadDocument,
  };

  return (
    <AuthContext.Provider value={contextValue}>
      {children}
    </AuthContext.Provider>
  );
};

// Hook to use auth context
export const useAuth = (): AuthContextType => {
  const context = useContext(AuthContext);
  if (context === undefined) {
    throw new Error('useAuth must be used within an AuthProvider');
  }
  return context;
};
