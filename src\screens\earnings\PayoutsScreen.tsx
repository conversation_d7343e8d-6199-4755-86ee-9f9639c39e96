import React, { useEffect, useState } from 'react';
import {
  View,
  Text,
  ScrollView,
  TouchableOpacity,
  RefreshControl,
  Alert,
} from 'react-native';
import { SafeAreaView } from 'react-native-safe-area-context';
import { Ionicons } from '@expo/vector-icons';
import { useNavigation } from '@react-navigation/native';
import { Card, Badge, Button, LoadingSpinner } from '../../components/ui';
import { useEarnings } from '../../context/EarningsContext';
import { PayoutInfo, PayoutStatus, PaymentMethod } from '../../types/earnings';
import { formatCurrency, formatDate } from '../../utils/helpers';

const PayoutsScreen: React.FC = () => {
  const navigation = useNavigation();
  const { state, fetchPayouts, fetchEarningsSummary } = useEarnings();
  const [refreshing, setRefreshing] = useState(false);

  useEffect(() => {
    loadData();
  }, []);

  const loadData = async () => {
    try {
      await Promise.all([
        fetchPayouts(),
        fetchEarningsSummary(),
      ]);
    } catch (error) {
      console.error('Error loading payout data:', error);
    }
  };

  const onRefresh = async () => {
    setRefreshing(true);
    await loadData();
    setRefreshing(false);
  };

  const handleInstantPayout = () => {
    Alert.alert(
      'Instant Payout',
      'Request instant payout for available earnings?',
      [
        { text: 'Cancel', style: 'cancel' },
        {
          text: 'Request',
          onPress: () => {
            // Implement instant payout logic
            Alert.alert('Success', 'Instant payout requested successfully!');
          },
        },
      ]
    );
  };

  const getStatusColor = (status: PayoutStatus) => {
    switch (status) {
      case PayoutStatus.COMPLETED:
        return '#10b981';
      case PayoutStatus.SCHEDULED:
        return '#f59e0b';
      case PayoutStatus.PROCESSING:
        return '#3b82f6';
      case PayoutStatus.FAILED:
        return '#ef4444';
      case PayoutStatus.CANCELLED:
        return '#6b7280';
      default:
        return '#6b7280';
    }
  };

  const getStatusText = (status: PayoutStatus) => {
    switch (status) {
      case PayoutStatus.COMPLETED:
        return 'Completed';
      case PayoutStatus.SCHEDULED:
        return 'Scheduled';
      case PayoutStatus.PROCESSING:
        return 'Processing';
      case PayoutStatus.FAILED:
        return 'Failed';
      case PayoutStatus.CANCELLED:
        return 'Cancelled';
      default:
        return status;
    }
  };

  const getPaymentMethodText = (method: PaymentMethod) => {
    switch (method) {
      case PaymentMethod.BANK_TRANSFER:
        return 'Bank Transfer';
      case PaymentMethod.PAYPAL:
        return 'PayPal';
      case PaymentMethod.CASH:
        return 'Cash';
      case PaymentMethod.DIGITAL_WALLET:
        return 'Digital Wallet';
      default:
        return method;
    }
  };

  const renderHeader = () => (
    <View style={{
      flexDirection: 'row',
      alignItems: 'center',
      paddingHorizontal: 20,
      paddingVertical: 16,
      backgroundColor: '#ffffff',
      borderBottomWidth: 1,
      borderBottomColor: '#e5e7eb',
    }}>
      <TouchableOpacity
        onPress={() => navigation.goBack()}
        style={{ marginRight: 16 }}
      >
        <Ionicons name="arrow-back" size={24} color="#374151" />
      </TouchableOpacity>
      
      <Text style={{ fontSize: 24, fontWeight: 'bold', color: '#111827' }}>
        Payouts
      </Text>
    </View>
  );

  const renderAvailableEarnings = () => {
    if (!state.summary) return null;

    const availableAmount = state.summary.week.total; // Simplified - would be actual available amount

    return (
      <Card variant="elevated" margin="md" padding="lg">
        <Text style={{ fontSize: 18, fontWeight: 'bold', color: '#111827', marginBottom: 16 }}>
          Available for Payout
        </Text>
        
        <View style={{ alignItems: 'center', marginBottom: 20 }}>
          <Text style={{ fontSize: 32, fontWeight: 'bold', color: '#f97316' }}>
            {formatCurrency(availableAmount)}
          </Text>
          <Text style={{ fontSize: 14, color: '#6b7280' }}>
            Ready for withdrawal
          </Text>
        </View>

        <View style={{ flexDirection: 'row', gap: 12 }}>
          <Button
            title="Instant Payout"
            leftIcon="flash-outline"
            onPress={handleInstantPayout}
            style={{ flex: 1 }}
          />
          
          <Button
            title="Schedule Payout"
            leftIcon="calendar-outline"
            variant="outline"
            style={{ flex: 1 }}
            onPress={() => {
              Alert.alert('Coming Soon', 'Scheduled payout feature will be available soon.');
            }}
          />
        </View>

        <Text style={{
          fontSize: 12,
          color: '#6b7280',
          textAlign: 'center',
          marginTop: 12,
        }}>
          Instant payouts have a small fee. Regular payouts are free.
        </Text>
      </Card>
    );
  };

  const renderPayoutCard = (payout: PayoutInfo) => (
    <Card key={payout.id} variant="elevated" margin="sm" padding="lg">
      <View style={{ flexDirection: 'row', justifyContent: 'space-between', marginBottom: 8 }}>
        <View style={{ flex: 1 }}>
          <Text style={{ fontSize: 18, fontWeight: 'bold', color: '#111827' }}>
            {formatCurrency(payout.amount)}
          </Text>
          <Text style={{ fontSize: 14, color: '#6b7280' }}>
            {getPaymentMethodText(payout.method)}
          </Text>
        </View>
        <Badge
          text={getStatusText(payout.status)}
          style={{
            backgroundColor: getStatusColor(payout.status),
          }}
        />
      </View>

      <View style={{
        flexDirection: 'row',
        justifyContent: 'space-between',
        paddingTop: 12,
        borderTopWidth: 1,
        borderTopColor: '#e5e7eb',
      }}>
        <View>
          <Text style={{ fontSize: 12, color: '#6b7280' }}>Scheduled</Text>
          <Text style={{ fontSize: 14, fontWeight: '500', color: '#111827' }}>
            {formatDate(payout.scheduledDate, 'DISPLAY_DATE')}
          </Text>
        </View>

        {payout.processedDate && (
          <View>
            <Text style={{ fontSize: 12, color: '#6b7280' }}>Processed</Text>
            <Text style={{ fontSize: 14, fontWeight: '500', color: '#111827' }}>
              {formatDate(payout.processedDate, 'DISPLAY_DATE')}
            </Text>
          </View>
        )}

        {payout.transactionId && (
          <View style={{ alignItems: 'flex-end' }}>
            <Text style={{ fontSize: 12, color: '#6b7280' }}>Transaction ID</Text>
            <Text style={{ fontSize: 14, fontWeight: '500', color: '#111827' }}>
              {payout.transactionId.slice(-8)}
            </Text>
          </View>
        )}
      </View>
    </Card>
  );

  const renderPayoutHistory = () => (
    <View style={{ marginTop: 8 }}>
      <Text style={{
        fontSize: 18,
        fontWeight: 'bold',
        color: '#111827',
        marginHorizontal: 20,
        marginBottom: 16,
      }}>
        Payout History
      </Text>
      
      {state.payouts.length === 0 ? (
        <Card variant="elevated" margin="md" padding="lg">
          <View style={{ alignItems: 'center', paddingVertical: 20 }}>
            <Ionicons name="card-outline" size={48} color="#d1d5db" />
            <Text style={{
              fontSize: 16,
              fontWeight: '500',
              color: '#374151',
              marginTop: 12,
              marginBottom: 4,
            }}>
              No Payouts Yet
            </Text>
            <Text style={{ fontSize: 14, color: '#6b7280', textAlign: 'center' }}>
              Your payout history will appear here once you request your first payout.
            </Text>
          </View>
        </Card>
      ) : (
        <View style={{ paddingVertical: 8 }}>
          {state.payouts.map(renderPayoutCard)}
        </View>
      )}
    </View>
  );

  if (state.isLoading && !refreshing) {
    return (
      <SafeAreaView style={{ flex: 1, backgroundColor: '#f8fafc' }}>
        {renderHeader()}
        <LoadingSpinner message="Loading payout information..." />
      </SafeAreaView>
    );
  }

  return (
    <SafeAreaView style={{ flex: 1, backgroundColor: '#f8fafc' }}>
      {renderHeader()}
      
      <ScrollView
        style={{ flex: 1 }}
        refreshControl={
          <RefreshControl refreshing={refreshing} onRefresh={onRefresh} />
        }
        showsVerticalScrollIndicator={false}
      >
        {renderAvailableEarnings()}
        {renderPayoutHistory()}
        
        {/* Bottom spacing */}
        <View style={{ height: 20 }} />
      </ScrollView>
    </SafeAreaView>
  );
};

export default PayoutsScreen;
