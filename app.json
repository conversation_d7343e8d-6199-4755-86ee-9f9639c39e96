{"expo": {"name": "rider_app", "slug": "rider_app", "version": "1.0.0", "orientation": "portrait", "icon": "./assets/icon.png", "userInterfaceStyle": "light", "newArchEnabled": true, "splash": {"image": "./assets/splash-icon.png", "resizeMode": "contain", "backgroundColor": "#ffffff"}, "assetBundlePatterns": ["**/*"], "ios": {"supportsTablet": true, "bundleIdentifier": "com.foodway.riderapp"}, "android": {"adaptiveIcon": {"foregroundImage": "./assets/adaptive-icon.png", "backgroundColor": "#ffffff"}, "edgeToEdgeEnabled": true, "package": "com.foodway.riderapp", "versionCode": 1, "enableProguardInReleaseBuilds": true, "enableSeparateBuildPerCPUArchitecture": true, "enableHermes": true}, "web": {"favicon": "./assets/favicon.png"}, "plugins": [["expo-build-properties", {"android": {"enableProguardInReleaseBuilds": true, "enableSeparateBuildPerCPUArchitecture": true, "packagingOptions": {"pickFirst": ["**/libc++_shared.so", "**/libjsc.so"]}, "proguardFiles": ["proguard-rules.pro"]}}]]}}