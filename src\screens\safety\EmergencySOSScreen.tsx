import React, { useState, useEffect, useRef } from 'react';
import {
  View,
  Text,
  TouchableOpacity,
  ScrollView,
  Alert,
  Linking,
  Animated,
  Vibration,
} from 'react-native';
import { SafeAreaView } from 'react-native-safe-area-context';
import { Ionicons } from '@expo/vector-icons';
import { useNavigation } from '@react-navigation/native';
import { StackNavigationProp } from '@react-navigation/stack';
import * as Location from 'expo-location';
import { SafetyStackParamList } from '../../types';
import {
  EmergencyType,
  EmergencyContact,
  EmergencyContactType,
  LocationSharingStatus,
} from '../../types/safety';

type EmergencySOSScreenNavigationProp = StackNavigationProp<SafetyStackParamList, 'EmergencySOS'>;

const EmergencySOSScreen: React.FC = () => {
  const navigation = useNavigation<EmergencySOSScreenNavigationProp>();
  const [isSOSActive, setIsSOSActive] = useState(false);
  const [countdown, setCountdown] = useState(0);
  const [selectedEmergencyType, setSelectedEmergencyType] = useState<EmergencyType | null>(null);
  const [currentLocation, setCurrentLocation] = useState<any>(null);
  const [locationSharing, setLocationSharing] = useState<LocationSharingStatus>(LocationSharingStatus.INACTIVE);
  
  const pulseAnim = useRef(new Animated.Value(1)).current;
  const countdownRef = useRef<NodeJS.Timeout | null>(null);

  // Mock emergency contacts
  const emergencyContacts: EmergencyContact[] = [
    {
      id: 'police',
      type: EmergencyContactType.POLICE,
      name: 'Police Emergency',
      phoneNumber: '15',
      description: 'Police emergency services',
      isActive: true,
      isPrimary: true,
      responseTime: '5-10 minutes',
      coverage: 'Nationwide',
      languages: ['Urdu', 'English'],
      availability: { is24x7: true },
    },
    {
      id: 'ambulance',
      type: EmergencyContactType.AMBULANCE,
      name: 'Ambulance Service',
      phoneNumber: '1122',
      description: 'Medical emergency services',
      isActive: true,
      isPrimary: true,
      responseTime: '8-15 minutes',
      coverage: 'Major cities',
      languages: ['Urdu', 'English'],
      availability: { is24x7: true },
    },
    {
      id: 'company',
      type: EmergencyContactType.COMPANY_EMERGENCY,
      name: 'FoodWay Emergency',
      phoneNumber: '+92-21-111-FOOD',
      description: '24/7 rider support hotline',
      isActive: true,
      isPrimary: false,
      responseTime: '2-5 minutes',
      coverage: 'Service areas',
      languages: ['Urdu', 'English', 'Punjabi'],
      availability: { is24x7: true },
    },
    {
      id: 'roadside',
      type: EmergencyContactType.ROADSIDE_ASSISTANCE,
      name: 'Roadside Assistance',
      phoneNumber: '+92-21-111-HELP',
      description: 'Vehicle breakdown assistance',
      isActive: true,
      isPrimary: false,
      responseTime: '15-30 minutes',
      coverage: 'Major highways',
      languages: ['Urdu', 'English'],
      availability: { is24x7: true },
    },
  ];

  const emergencyTypes = [
    {
      type: EmergencyType.MEDICAL,
      title: 'Medical Emergency',
      description: 'Health emergency or injury',
      icon: 'medical',
      color: '#dc2626',
      contacts: ['ambulance', 'company'],
    },
    {
      type: EmergencyType.ACCIDENT,
      title: 'Traffic Accident',
      description: 'Vehicle accident or collision',
      icon: 'car-sport',
      color: '#ea580c',
      contacts: ['police', 'company', 'roadside'],
    },
    {
      type: EmergencyType.THEFT,
      title: 'Theft/Robbery',
      description: 'Theft or robbery incident',
      icon: 'shield-outline',
      color: '#dc2626',
      contacts: ['police', 'company'],
    },
    {
      type: EmergencyType.HARASSMENT,
      title: 'Harassment/Threat',
      description: 'Harassment or threatening behavior',
      icon: 'warning',
      color: '#dc2626',
      contacts: ['police', 'company'],
    },
    {
      type: EmergencyType.VEHICLE_BREAKDOWN,
      title: 'Vehicle Breakdown',
      description: 'Vehicle malfunction or breakdown',
      icon: 'construct',
      color: '#f59e0b',
      contacts: ['roadside', 'company'],
    },
    {
      type: EmergencyType.LOST,
      title: 'Lost/Stranded',
      description: 'Lost or stranded in unfamiliar area',
      icon: 'location-outline',
      color: '#0891b2',
      contacts: ['company'],
    },
  ];

  useEffect(() => {
    getCurrentLocation();
    startPulseAnimation();
  }, []);

  useEffect(() => {
    if (countdown > 0) {
      countdownRef.current = setTimeout(() => {
        setCountdown(countdown - 1);
        Vibration.vibrate(100);
      }, 1000);
    } else if (countdown === 0 && isSOSActive) {
      activateEmergencySOS();
    }

    return () => {
      if (countdownRef.current) {
        clearTimeout(countdownRef.current);
      }
    };
  }, [countdown, isSOSActive]);

  const startPulseAnimation = () => {
    Animated.loop(
      Animated.sequence([
        Animated.timing(pulseAnim, {
          toValue: 1.2,
          duration: 1000,
          useNativeDriver: true,
        }),
        Animated.timing(pulseAnim, {
          toValue: 1,
          duration: 1000,
          useNativeDriver: true,
        }),
      ])
    ).start();
  };

  const getCurrentLocation = async () => {
    try {
      const { status } = await Location.requestForegroundPermissionsAsync();
      if (status !== 'granted') {
        Alert.alert('Permission Denied', 'Location permission is required for emergency services.');
        return;
      }

      const location = await Location.getCurrentPositionAsync({});
      const address = await Location.reverseGeocodeAsync({
        latitude: location.coords.latitude,
        longitude: location.coords.longitude,
      });

      setCurrentLocation({
        latitude: location.coords.latitude,
        longitude: location.coords.longitude,
        address: address[0] ? `${address[0].street}, ${address[0].city}` : 'Unknown location',
        timestamp: new Date().toISOString(),
      });
    } catch (error) {
      console.error('Error getting location:', error);
    }
  };

  const startSOSCountdown = (emergencyType: EmergencyType) => {
    setSelectedEmergencyType(emergencyType);
    setIsSOSActive(true);
    setCountdown(5); // 5 second countdown
    Vibration.vibrate([0, 500, 200, 500]);
  };

  const cancelSOS = () => {
    setIsSOSActive(false);
    setCountdown(0);
    setSelectedEmergencyType(null);
    if (countdownRef.current) {
      clearTimeout(countdownRef.current);
    }
  };

  const activateEmergencySOS = async () => {
    if (!selectedEmergencyType) return;

    try {
      // Get relevant contacts for this emergency type
      const emergencyTypeConfig = emergencyTypes.find(et => et.type === selectedEmergencyType);
      const relevantContacts = emergencyContacts.filter(contact => 
        emergencyTypeConfig?.contacts.includes(contact.id)
      );

      // Start location sharing
      setLocationSharing(LocationSharingStatus.ACTIVE);

      // Show confirmation dialog
      Alert.alert(
        'Emergency SOS Activated',
        `Emergency contacts will be notified. Your location is being shared.\n\nLocation: ${currentLocation?.address || 'Unknown'}`,
        [
          {
            text: 'Call Emergency Services',
            onPress: () => callEmergencyNumber(relevantContacts[0]?.phoneNumber || '15'),
            style: 'default',
          },
          {
            text: 'Contact Company',
            onPress: () => callEmergencyNumber('+92-21-111-FOOD'),
            style: 'default',
          },
          {
            text: 'End SOS',
            onPress: () => endSOS(),
            style: 'cancel',
          },
        ]
      );

      // Simulate notifying emergency contacts
      setTimeout(() => {
        Alert.alert(
          'Emergency Contacts Notified',
          'Your emergency contacts and FoodWay support have been notified of your situation.'
        );
      }, 2000);

    } catch (error) {
      console.error('Error activating SOS:', error);
      Alert.alert('Error', 'Failed to activate emergency SOS. Please call emergency services directly.');
    }
  };

  const callEmergencyNumber = (phoneNumber: string) => {
    const url = `tel:${phoneNumber}`;
    Linking.canOpenURL(url)
      .then((supported) => {
        if (supported) {
          Linking.openURL(url);
        } else {
          Alert.alert('Error', 'Unable to make phone call');
        }
      })
      .catch((error) => console.error('Error making call:', error));
  };

  const endSOS = () => {
    setIsSOSActive(false);
    setCountdown(0);
    setSelectedEmergencyType(null);
    setLocationSharing(LocationSharingStatus.INACTIVE);
    
    Alert.alert(
      'SOS Ended',
      'Emergency SOS has been deactivated. Location sharing has been stopped.'
    );
  };

  const toggleLocationSharing = () => {
    const newStatus = locationSharing === LocationSharingStatus.ACTIVE 
      ? LocationSharingStatus.INACTIVE 
      : LocationSharingStatus.ACTIVE;
    
    setLocationSharing(newStatus);
    
    Alert.alert(
      'Location Sharing',
      newStatus === LocationSharingStatus.ACTIVE 
        ? 'Your location is now being shared with emergency contacts.'
        : 'Location sharing has been stopped.'
    );
  };

  const renderEmergencyType = (emergencyType: typeof emergencyTypes[0]) => (
    <TouchableOpacity
      key={emergencyType.type}
      onPress={() => startSOSCountdown(emergencyType.type)}
      disabled={isSOSActive}
      style={{
        backgroundColor: 'white',
        marginHorizontal: 16,
        marginVertical: 8,
        borderRadius: 12,
        padding: 16,
        flexDirection: 'row',
        alignItems: 'center',
        borderWidth: 2,
        borderColor: emergencyType.color,
        opacity: isSOSActive ? 0.5 : 1,
        shadowColor: emergencyType.color,
        shadowOffset: { width: 0, height: 2 },
        shadowOpacity: 0.1,
        shadowRadius: 4,
        elevation: 3,
      }}
    >
      <View style={{
        width: 48,
        height: 48,
        backgroundColor: `${emergencyType.color}15`,
        borderRadius: 24,
        alignItems: 'center',
        justifyContent: 'center',
        marginRight: 16,
      }}>
        <Ionicons
          name={emergencyType.icon as any}
          size={24}
          color={emergencyType.color}
        />
      </View>
      
      <View style={{ flex: 1 }}>
        <Text style={{
          fontSize: 16,
          fontWeight: '600',
          color: '#1f2937',
          marginBottom: 4,
        }}>
          {emergencyType.title}
        </Text>
        <Text style={{
          fontSize: 14,
          color: '#6b7280',
          lineHeight: 20,
        }}>
          {emergencyType.description}
        </Text>
      </View>
      
      <Ionicons
        name="chevron-forward"
        size={20}
        color={emergencyType.color}
      />
    </TouchableOpacity>
  );

  const renderEmergencyContact = (contact: EmergencyContact) => (
    <TouchableOpacity
      key={contact.id}
      onPress={() => callEmergencyNumber(contact.phoneNumber)}
      style={{
        backgroundColor: 'white',
        marginHorizontal: 16,
        marginVertical: 4,
        borderRadius: 8,
        padding: 12,
        flexDirection: 'row',
        alignItems: 'center',
        borderWidth: 1,
        borderColor: '#e5e7eb',
      }}
    >
      <View style={{
        width: 40,
        height: 40,
        backgroundColor: contact.isPrimary ? '#dc262615' : '#f3f4f6',
        borderRadius: 20,
        alignItems: 'center',
        justifyContent: 'center',
        marginRight: 12,
      }}>
        <Ionicons
          name="call"
          size={20}
          color={contact.isPrimary ? '#dc2626' : '#6b7280'}
        />
      </View>
      
      <View style={{ flex: 1 }}>
        <Text style={{
          fontSize: 14,
          fontWeight: '600',
          color: '#1f2937',
        }}>
          {contact.name}
        </Text>
        <Text style={{
          fontSize: 12,
          color: '#6b7280',
        }}>
          {contact.phoneNumber}
        </Text>
      </View>
      
      {contact.availability.is24x7 && (
        <View style={{
          backgroundColor: '#10b98115',
          paddingHorizontal: 8,
          paddingVertical: 4,
          borderRadius: 12,
        }}>
          <Text style={{
            fontSize: 10,
            color: '#10b981',
            fontWeight: '500',
          }}>
            24/7
          </Text>
        </View>
      )}
    </TouchableOpacity>
  );

  return (
    <SafeAreaView style={{ flex: 1, backgroundColor: '#f9fafb' }}>
      {/* Header */}
      <View style={{
        flexDirection: 'row',
        alignItems: 'center',
        paddingHorizontal: 16,
        paddingVertical: 12,
        backgroundColor: 'white',
        borderBottomWidth: 1,
        borderBottomColor: '#e5e7eb',
      }}>
        <TouchableOpacity
          onPress={() => navigation.goBack()}
          style={{
            width: 40,
            height: 40,
            alignItems: 'center',
            justifyContent: 'center',
            marginRight: 8,
          }}
        >
          <Ionicons name="arrow-back" size={24} color="#374151" />
        </TouchableOpacity>
        
        <Text style={{
          fontSize: 18,
          fontWeight: '600',
          color: '#1f2937',
          flex: 1,
        }}>
          Emergency SOS
        </Text>
        
        <TouchableOpacity
          onPress={toggleLocationSharing}
          style={{
            flexDirection: 'row',
            alignItems: 'center',
            backgroundColor: locationSharing === LocationSharingStatus.ACTIVE ? '#10b98115' : '#f3f4f6',
            paddingHorizontal: 12,
            paddingVertical: 6,
            borderRadius: 16,
          }}
        >
          <Ionicons
            name="location"
            size={16}
            color={locationSharing === LocationSharingStatus.ACTIVE ? '#10b981' : '#6b7280'}
          />
          <Text style={{
            fontSize: 12,
            color: locationSharing === LocationSharingStatus.ACTIVE ? '#10b981' : '#6b7280',
            fontWeight: '500',
            marginLeft: 4,
          }}>
            {locationSharing === LocationSharingStatus.ACTIVE ? 'Sharing' : 'Share Location'}
          </Text>
        </TouchableOpacity>
      </View>

      <ScrollView style={{ flex: 1 }}>
        {/* SOS Status */}
        {isSOSActive && (
          <View style={{
            backgroundColor: '#fef2f2',
            marginHorizontal: 16,
            marginTop: 16,
            borderRadius: 12,
            padding: 16,
            borderWidth: 2,
            borderColor: '#fecaca',
            alignItems: 'center',
          }}>
            <Animated.View style={{
              transform: [{ scale: pulseAnim }],
              marginBottom: 12,
            }}>
              <View style={{
                width: 80,
                height: 80,
                backgroundColor: '#dc2626',
                borderRadius: 40,
                alignItems: 'center',
                justifyContent: 'center',
              }}>
                <Text style={{
                  fontSize: 32,
                  fontWeight: 'bold',
                  color: 'white',
                }}>
                  {countdown}
                </Text>
              </View>
            </Animated.View>
            
            <Text style={{
              fontSize: 18,
              fontWeight: '600',
              color: '#dc2626',
              marginBottom: 8,
            }}>
              Emergency SOS Activating
            </Text>
            
            <Text style={{
              fontSize: 14,
              color: '#991b1b',
              textAlign: 'center',
              marginBottom: 16,
            }}>
              Emergency services will be contacted in {countdown} seconds
            </Text>
            
            <TouchableOpacity
              onPress={cancelSOS}
              style={{
                backgroundColor: '#374151',
                paddingHorizontal: 24,
                paddingVertical: 12,
                borderRadius: 8,
              }}
            >
              <Text style={{
                color: 'white',
                fontWeight: '600',
                fontSize: 16,
              }}>
                Cancel SOS
              </Text>
            </TouchableOpacity>
          </View>
        )}

        {/* Current Location */}
        {currentLocation && (
          <View style={{
            backgroundColor: 'white',
            marginHorizontal: 16,
            marginTop: 16,
            borderRadius: 12,
            padding: 16,
            borderWidth: 1,
            borderColor: '#e5e7eb',
          }}>
            <View style={{
              flexDirection: 'row',
              alignItems: 'center',
              marginBottom: 8,
            }}>
              <Ionicons name="location" size={20} color="#10b981" />
              <Text style={{
                fontSize: 16,
                fontWeight: '600',
                color: '#1f2937',
                marginLeft: 8,
              }}>
                Current Location
              </Text>
            </View>
            <Text style={{
              fontSize: 14,
              color: '#6b7280',
              lineHeight: 20,
            }}>
              {currentLocation.address}
            </Text>
          </View>
        )}

        {/* Emergency Types */}
        <View style={{ marginTop: 24 }}>
          <Text style={{
            fontSize: 18,
            fontWeight: '600',
            color: '#1f2937',
            paddingHorizontal: 16,
            marginBottom: 12,
          }}>
            Select Emergency Type
          </Text>
          
          {emergencyTypes.map(emergencyType => renderEmergencyType(emergencyType))}
        </View>

        {/* Quick Dial Emergency Contacts */}
        <View style={{ marginTop: 32 }}>
          <Text style={{
            fontSize: 18,
            fontWeight: '600',
            color: '#1f2937',
            paddingHorizontal: 16,
            marginBottom: 12,
          }}>
            Quick Dial Emergency Contacts
          </Text>
          
          {emergencyContacts.map(contact => renderEmergencyContact(contact))}
        </View>

        {/* Safety Tips */}
        <View style={{
          backgroundColor: '#f0f9ff',
          marginHorizontal: 16,
          marginTop: 24,
          marginBottom: 32,
          borderRadius: 12,
          padding: 16,
          borderWidth: 1,
          borderColor: '#bae6fd',
        }}>
          <View style={{
            flexDirection: 'row',
            alignItems: 'center',
            marginBottom: 12,
          }}>
            <Ionicons name="shield-checkmark" size={20} color="#0284c7" />
            <Text style={{
              fontSize: 16,
              fontWeight: '600',
              color: '#0284c7',
              marginLeft: 8,
            }}>
              Safety Tips
            </Text>
          </View>
          
          <Text style={{
            fontSize: 14,
            color: '#0369a1',
            lineHeight: 20,
            marginBottom: 8,
          }}>
            • Stay calm and assess the situation
          </Text>
          <Text style={{
            fontSize: 14,
            color: '#0369a1',
            lineHeight: 20,
            marginBottom: 8,
          }}>
            • Move to a safe location if possible
          </Text>
          <Text style={{
            fontSize: 14,
            color: '#0369a1',
            lineHeight: 20,
            marginBottom: 8,
          }}>
            • Share your location with trusted contacts
          </Text>
          <Text style={{
            fontSize: 14,
            color: '#0369a1',
            lineHeight: 20,
          }}>
            • Keep your phone charged and accessible
          </Text>
        </View>
      </ScrollView>
    </SafeAreaView>
  );
};

export default EmergencySOSScreen;
