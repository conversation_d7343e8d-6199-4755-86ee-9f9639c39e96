import React, { useState, useEffect } from 'react';
import {
  View,
  Text,
  TouchableOpacity,
  ScrollView,
  Modal,
  Dimensions,
} from 'react-native';
import { SafeAreaView } from 'react-native-safe-area-context';
import { Ionicons } from '@expo/vector-icons';
import { useNavigation } from '@react-navigation/native';
import { StackNavigationProp } from '@react-navigation/stack';
import { AdvancedToolsStackParamList } from '../../types';
import {
  HeatmapArea,
  DemandLevel,
  AreaType,
  DemandForecast,
} from '../../types/advancedTools';

type HeatmapScreenNavigationProp = StackNavigationProp<AdvancedToolsStackParamList, 'Heatmap'>;

const { width, height } = Dimensions.get('window');

const HeatmapScreen: React.FC = () => {
  const navigation = useNavigation<HeatmapScreenNavigationProp>();
  const [heatmapAreas, setHeatmapAreas] = useState<HeatmapArea[]>([]);
  const [selectedArea, setSelectedArea] = useState<HeatmapArea | null>(null);
  const [showAreaDetails, setShowAreaDetails] = useState(false);
  const [viewMode, setViewMode] = useState<'demand' | 'surge' | 'earnings'>('demand');
  const [loading, setLoading] = useState(true);

  // Mock heatmap data
  const mockHeatmapAreas: HeatmapArea[] = [
    {
      id: 'area-1',
      name: 'Clifton Block 2',
      coordinates: { latitude: 24.8138, longitude: 67.0299 },
      radius: 1000,
      demandLevel: DemandLevel.VERY_HIGH,
      areaType: AreaType.COMMERCIAL,
      surgeMultiplier: 2.5,
      bonusAmount: 150,
      estimatedOrders: 45,
      averageDeliveryTime: 18,
      averageEarnings: 320,
      activeRiders: 12,
      lastUpdated: new Date().toISOString(),
      peakHours: [
        { start: '12:00', end: '14:00' },
        { start: '19:00', end: '22:00' },
      ],
      weatherImpact: 0.2,
    },
    {
      id: 'area-2',
      name: 'DHA Phase 5',
      coordinates: { latitude: 24.8059, longitude: 67.0756 },
      radius: 1500,
      demandLevel: DemandLevel.HIGH,
      areaType: AreaType.RESIDENTIAL,
      surgeMultiplier: 1.8,
      bonusAmount: 100,
      estimatedOrders: 32,
      averageDeliveryTime: 22,
      averageEarnings: 280,
      activeRiders: 8,
      lastUpdated: new Date().toISOString(),
      peakHours: [
        { start: '18:00', end: '21:00' },
      ],
      weatherImpact: 0.1,
    },
    {
      id: 'area-3',
      name: 'Gulshan-e-Iqbal',
      coordinates: { latitude: 24.9056, longitude: 67.0822 },
      radius: 2000,
      demandLevel: DemandLevel.MEDIUM,
      areaType: AreaType.RESTAURANT_CLUSTER,
      surgeMultiplier: 1.3,
      bonusAmount: 75,
      estimatedOrders: 28,
      averageDeliveryTime: 25,
      averageEarnings: 220,
      activeRiders: 15,
      lastUpdated: new Date().toISOString(),
      peakHours: [
        { start: '13:00', end: '15:00' },
        { start: '20:00', end: '23:00' },
      ],
      weatherImpact: -0.1,
    },
    {
      id: 'area-4',
      name: 'Saddar Town',
      coordinates: { latitude: 24.8615, longitude: 67.0099 },
      radius: 800,
      demandLevel: DemandLevel.SURGE,
      areaType: AreaType.COMMERCIAL,
      surgeMultiplier: 3.2,
      bonusAmount: 200,
      estimatedOrders: 52,
      averageDeliveryTime: 15,
      averageEarnings: 380,
      activeRiders: 6,
      lastUpdated: new Date().toISOString(),
      peakHours: [
        { start: '11:30', end: '14:30' },
        { start: '18:30', end: '21:30' },
      ],
      weatherImpact: 0.3,
      eventImpact: {
        eventName: 'Shopping Festival',
        impactLevel: 0.4,
        duration: {
          start: new Date().toISOString(),
          end: new Date(Date.now() + 4 * 60 * 60 * 1000).toISOString(),
        },
      },
    },
    {
      id: 'area-5',
      name: 'Korangi Industrial',
      coordinates: { latitude: 24.8546, longitude: 67.1598 },
      radius: 1200,
      demandLevel: DemandLevel.LOW,
      areaType: AreaType.OFFICE_COMPLEX,
      surgeMultiplier: 1.0,
      bonusAmount: 50,
      estimatedOrders: 18,
      averageDeliveryTime: 30,
      averageEarnings: 180,
      activeRiders: 20,
      lastUpdated: new Date().toISOString(),
      peakHours: [
        { start: '12:00', end: '13:30' },
      ],
      weatherImpact: 0.0,
    },
    {
      id: 'area-6',
      name: 'University Road',
      coordinates: { latitude: 24.9265, longitude: 67.1121 },
      radius: 1800,
      demandLevel: DemandLevel.HIGH,
      areaType: AreaType.UNIVERSITY,
      surgeMultiplier: 2.0,
      bonusAmount: 120,
      estimatedOrders: 38,
      averageDeliveryTime: 20,
      averageEarnings: 260,
      activeRiders: 10,
      lastUpdated: new Date().toISOString(),
      peakHours: [
        { start: '12:30', end: '14:00' },
        { start: '19:00', end: '21:30' },
      ],
      weatherImpact: 0.15,
    },
  ];

  useEffect(() => {
    loadHeatmapData();
  }, []);

  const loadHeatmapData = async () => {
    setLoading(true);
    try {
      // Simulate API call
      await new Promise(resolve => setTimeout(resolve, 1500));
      setHeatmapAreas(mockHeatmapAreas);
    } catch (error) {
      console.error('Error loading heatmap data:', error);
    } finally {
      setLoading(false);
    }
  };

  const getDemandColor = (demandLevel: DemandLevel): string => {
    switch (demandLevel) {
      case DemandLevel.VERY_LOW:
        return '#10b981';
      case DemandLevel.LOW:
        return '#84cc16';
      case DemandLevel.MEDIUM:
        return '#f59e0b';
      case DemandLevel.HIGH:
        return '#f97316';
      case DemandLevel.VERY_HIGH:
        return '#ef4444';
      case DemandLevel.SURGE:
        return '#dc2626';
      default:
        return '#6b7280';
    }
  };

  const getDemandLabel = (demandLevel: DemandLevel): string => {
    switch (demandLevel) {
      case DemandLevel.VERY_LOW:
        return 'Very Low';
      case DemandLevel.LOW:
        return 'Low';
      case DemandLevel.MEDIUM:
        return 'Medium';
      case DemandLevel.HIGH:
        return 'High';
      case DemandLevel.VERY_HIGH:
        return 'Very High';
      case DemandLevel.SURGE:
        return 'SURGE';
      default:
        return 'Unknown';
    }
  };

  const getAreaTypeIcon = (areaType: AreaType): string => {
    switch (areaType) {
      case AreaType.RESIDENTIAL:
        return 'home-outline';
      case AreaType.COMMERCIAL:
        return 'storefront-outline';
      case AreaType.RESTAURANT_CLUSTER:
        return 'restaurant-outline';
      case AreaType.SHOPPING_MALL:
        return 'bag-outline';
      case AreaType.OFFICE_COMPLEX:
        return 'business-outline';
      case AreaType.UNIVERSITY:
        return 'school-outline';
      case AreaType.HOSPITAL:
        return 'medical-outline';
      case AreaType.AIRPORT:
        return 'airplane-outline';
      default:
        return 'location-outline';
    }
  };

  const getViewModeValue = (area: HeatmapArea): number => {
    switch (viewMode) {
      case 'demand':
        return area.estimatedOrders;
      case 'surge':
        return area.surgeMultiplier;
      case 'earnings':
        return area.averageEarnings;
      default:
        return 0;
    }
  };

  const getViewModeLabel = (): string => {
    switch (viewMode) {
      case 'demand':
        return 'Orders';
      case 'surge':
        return 'Surge';
      case 'earnings':
        return 'Earnings';
      default:
        return '';
    }
  };

  const openAreaDetails = (area: HeatmapArea) => {
    setSelectedArea(area);
    setShowAreaDetails(true);
  };

  const renderHeatmapArea = (area: HeatmapArea) => {
    const demandColor = getDemandColor(area.demandLevel);
    const viewModeValue = getViewModeValue(area);

    return (
      <TouchableOpacity
        key={area.id}
        onPress={() => openAreaDetails(area)}
        style={{
          backgroundColor: 'white',
          marginHorizontal: 16,
          marginVertical: 8,
          borderRadius: 12,
          padding: 16,
          borderLeftWidth: 4,
          borderLeftColor: demandColor,
          shadowColor: '#000',
          shadowOffset: { width: 0, height: 2 },
          shadowOpacity: 0.1,
          shadowRadius: 4,
          elevation: 3,
        }}
      >
        <View style={{
          flexDirection: 'row',
          alignItems: 'center',
          justifyContent: 'space-between',
          marginBottom: 12,
        }}>
          <View style={{ flex: 1 }}>
            <View style={{
              flexDirection: 'row',
              alignItems: 'center',
              marginBottom: 4,
            }}>
              <Ionicons
                name={getAreaTypeIcon(area.areaType) as any}
                size={20}
                color="#6b7280"
                style={{ marginRight: 8 }}
              />
              <Text style={{
                fontSize: 16,
                fontWeight: '600',
                color: '#1f2937',
                flex: 1,
              }}>
                {area.name}
              </Text>
            </View>

            <View style={{
              flexDirection: 'row',
              alignItems: 'center',
            }}>
              <View style={{
                backgroundColor: `${demandColor}15`,
                paddingHorizontal: 8,
                paddingVertical: 4,
                borderRadius: 12,
                marginRight: 8,
              }}>
                <Text style={{
                  fontSize: 12,
                  color: demandColor,
                  fontWeight: '600',
                }}>
                  {getDemandLabel(area.demandLevel)}
                </Text>
              </View>

              {area.demandLevel === DemandLevel.SURGE && (
                <View style={{
                  backgroundColor: '#fef3c7',
                  paddingHorizontal: 8,
                  paddingVertical: 4,
                  borderRadius: 12,
                  marginRight: 8,
                }}>
                  <Text style={{
                    fontSize: 12,
                    color: '#d97706',
                    fontWeight: '600',
                  }}>
                    {area.surgeMultiplier}x SURGE
                  </Text>
                </View>
              )}

              {area.bonusAmount > 0 && (
                <View style={{
                  backgroundColor: '#dcfce7',
                  paddingHorizontal: 8,
                  paddingVertical: 4,
                  borderRadius: 12,
                }}>
                  <Text style={{
                    fontSize: 12,
                    color: '#16a34a',
                    fontWeight: '600',
                  }}>
                    +Rs.{area.bonusAmount}
                  </Text>
                </View>
              )}
            </View>
          </View>

          <View style={{ alignItems: 'flex-end' }}>
            <Text style={{
              fontSize: 20,
              fontWeight: 'bold',
              color: demandColor,
            }}>
              {viewMode === 'surge' ? `${viewModeValue}x` :
               viewMode === 'earnings' ? `Rs.${viewModeValue}` :
               `${viewModeValue}`}
            </Text>
            <Text style={{
              fontSize: 12,
              color: '#6b7280',
            }}>
              {getViewModeLabel()}
            </Text>
          </View>
        </View>

        <View style={{
          flexDirection: 'row',
          justifyContent: 'space-between',
          paddingTop: 12,
          borderTopWidth: 1,
          borderTopColor: '#f3f4f6',
        }}>
          <View style={{ alignItems: 'center', flex: 1 }}>
            <Text style={{
              fontSize: 14,
              fontWeight: '600',
              color: '#1f2937',
            }}>
              {area.activeRiders}
            </Text>
            <Text style={{
              fontSize: 12,
              color: '#6b7280',
            }}>
              Riders
            </Text>
          </View>

          <View style={{ alignItems: 'center', flex: 1 }}>
            <Text style={{
              fontSize: 14,
              fontWeight: '600',
              color: '#1f2937',
            }}>
              {area.averageDeliveryTime}m
            </Text>
            <Text style={{
              fontSize: 12,
              color: '#6b7280',
            }}>
              Avg Time
            </Text>
          </View>

          <View style={{ alignItems: 'center', flex: 1 }}>
            <Text style={{
              fontSize: 14,
              fontWeight: '600',
              color: '#1f2937',
            }}>
              {area.radius}m
            </Text>
            <Text style={{
              fontSize: 12,
              color: '#6b7280',
            }}>
              Radius
            </Text>
          </View>
        </View>

        {area.eventImpact && (
          <View style={{
            backgroundColor: '#fef3c7',
            padding: 12,
            borderRadius: 8,
            marginTop: 12,
          }}>
            <View style={{
              flexDirection: 'row',
              alignItems: 'center',
              marginBottom: 4,
            }}>
              <Ionicons name="star" size={16} color="#d97706" />
              <Text style={{
                fontSize: 14,
                fontWeight: '600',
                color: '#d97706',
                marginLeft: 6,
              }}>
                Special Event
              </Text>
            </View>
            <Text style={{
              fontSize: 12,
              color: '#92400e',
            }}>
              {area.eventImpact.eventName} - Higher demand expected
            </Text>
          </View>
        )}
      </TouchableOpacity>
    );
  };

  return (
    <SafeAreaView style={{ flex: 1, backgroundColor: '#f9fafb' }}>
      {/* Header */}
      <View style={{
        flexDirection: 'row',
        alignItems: 'center',
        paddingHorizontal: 16,
        paddingVertical: 12,
        backgroundColor: 'white',
        borderBottomWidth: 1,
        borderBottomColor: '#e5e7eb',
      }}>
        <TouchableOpacity
          onPress={() => navigation.goBack()}
          style={{
            width: 40,
            height: 40,
            alignItems: 'center',
            justifyContent: 'center',
            marginRight: 8,
          }}
        >
          <Ionicons name="arrow-back" size={24} color="#374151" />
        </TouchableOpacity>

        <Text style={{
          fontSize: 18,
          fontWeight: '600',
          color: '#1f2937',
          flex: 1,
        }}>
          Demand Heatmap
        </Text>

        <TouchableOpacity
          onPress={loadHeatmapData}
          style={{
            width: 40,
            height: 40,
            alignItems: 'center',
            justifyContent: 'center',
          }}
        >
          <Ionicons name="refresh" size={24} color="#374151" />
        </TouchableOpacity>
      </View>

      {/* View Mode Selector */}
      <View style={{
        flexDirection: 'row',
        paddingHorizontal: 16,
        paddingVertical: 12,
        backgroundColor: 'white',
        borderBottomWidth: 1,
        borderBottomColor: '#e5e7eb',
      }}>
        {(['demand', 'surge', 'earnings'] as const).map((mode) => (
          <TouchableOpacity
            key={mode}
            onPress={() => setViewMode(mode)}
            style={{
              flex: 1,
              paddingVertical: 8,
              paddingHorizontal: 12,
              marginHorizontal: 4,
              borderRadius: 8,
              backgroundColor: viewMode === mode ? '#f97316' : '#f3f4f6',
            }}
          >
            <Text style={{
              textAlign: 'center',
              fontSize: 14,
              fontWeight: '600',
              color: viewMode === mode ? 'white' : '#6b7280',
              textTransform: 'capitalize',
            }}>
              {mode}
            </Text>
          </TouchableOpacity>
        ))}
      </View>

      {/* Heatmap Areas List */}
      <ScrollView style={{ flex: 1 }}>
        {loading ? (
          <View style={{
            flex: 1,
            alignItems: 'center',
            justifyContent: 'center',
            paddingVertical: 40,
          }}>
            <Text style={{
              fontSize: 16,
              color: '#6b7280',
            }}>
              Loading heatmap data...
            </Text>
          </View>
        ) : (
          <>
            {/* Summary Stats */}
            <View style={{
              backgroundColor: 'white',
              marginHorizontal: 16,
              marginTop: 16,
              borderRadius: 12,
              padding: 16,
              borderWidth: 1,
              borderColor: '#e5e7eb',
            }}>
              <Text style={{
                fontSize: 16,
                fontWeight: '600',
                color: '#1f2937',
                marginBottom: 12,
              }}>
                Live Overview
              </Text>

              <View style={{
                flexDirection: 'row',
                justifyContent: 'space-between',
              }}>
                <View style={{ alignItems: 'center', flex: 1 }}>
                  <Text style={{
                    fontSize: 20,
                    fontWeight: 'bold',
                    color: '#dc2626',
                  }}>
                    {heatmapAreas.filter(a => a.demandLevel === DemandLevel.SURGE).length}
                  </Text>
                  <Text style={{
                    fontSize: 12,
                    color: '#6b7280',
                    textAlign: 'center',
                  }}>
                    Surge Areas
                  </Text>
                </View>

                <View style={{ alignItems: 'center', flex: 1 }}>
                  <Text style={{
                    fontSize: 20,
                    fontWeight: 'bold',
                    color: '#f97316',
                  }}>
                    {heatmapAreas.filter(a => a.demandLevel === DemandLevel.HIGH || a.demandLevel === DemandLevel.VERY_HIGH).length}
                  </Text>
                  <Text style={{
                    fontSize: 12,
                    color: '#6b7280',
                    textAlign: 'center',
                  }}>
                    High Demand
                  </Text>
                </View>

                <View style={{ alignItems: 'center', flex: 1 }}>
                  <Text style={{
                    fontSize: 20,
                    fontWeight: 'bold',
                    color: '#10b981',
                  }}>
                    Rs.{Math.round(heatmapAreas.reduce((sum, a) => sum + a.bonusAmount, 0) / heatmapAreas.length)}
                  </Text>
                  <Text style={{
                    fontSize: 12,
                    color: '#6b7280',
                    textAlign: 'center',
                  }}>
                    Avg Bonus
                  </Text>
                </View>
              </View>
            </View>

            {/* Areas List */}
            <View style={{ paddingTop: 8, paddingBottom: 32 }}>
              {heatmapAreas
                .sort((a, b) => {
                  // Sort by demand level (surge first, then high to low)
                  const demandOrder = {
                    [DemandLevel.SURGE]: 6,
                    [DemandLevel.VERY_HIGH]: 5,
                    [DemandLevel.HIGH]: 4,
                    [DemandLevel.MEDIUM]: 3,
                    [DemandLevel.LOW]: 2,
                    [DemandLevel.VERY_LOW]: 1,
                  };
                  return demandOrder[b.demandLevel] - demandOrder[a.demandLevel];
                })
                .map(area => renderHeatmapArea(area))}
            </View>
          </>
        )}
      </ScrollView>

      {/* Area Details Modal */}
      <Modal
        visible={showAreaDetails}
        animationType="slide"
        presentationStyle="pageSheet"
      >
        {selectedArea && (
          <SafeAreaView style={{ flex: 1, backgroundColor: '#f9fafb' }}>
            {/* Modal Header */}
            <View style={{
              flexDirection: 'row',
              alignItems: 'center',
              paddingHorizontal: 16,
              paddingVertical: 12,
              backgroundColor: 'white',
              borderBottomWidth: 1,
              borderBottomColor: '#e5e7eb',
            }}>
              <TouchableOpacity
                onPress={() => setShowAreaDetails(false)}
                style={{
                  width: 40,
                  height: 40,
                  alignItems: 'center',
                  justifyContent: 'center',
                  marginRight: 8,
                }}
              >
                <Ionicons name="close" size={24} color="#374151" />
              </TouchableOpacity>

              <View style={{ flex: 1 }}>
                <Text style={{
                  fontSize: 18,
                  fontWeight: '600',
                  color: '#1f2937',
                }}>
                  {selectedArea.name}
                </Text>
                <Text style={{
                  fontSize: 14,
                  color: '#6b7280',
                }}>
                  Area Details & Analytics
                </Text>
              </View>
            </View>

            <ScrollView style={{ flex: 1, padding: 16 }}>
              {/* Demand Status */}
              <View style={{
                backgroundColor: 'white',
                borderRadius: 12,
                padding: 16,
                marginBottom: 16,
              }}>
                <Text style={{
                  fontSize: 16,
                  fontWeight: '600',
                  color: '#1f2937',
                  marginBottom: 12,
                }}>
                  Current Status
                </Text>

                <View style={{
                  flexDirection: 'row',
                  alignItems: 'center',
                  marginBottom: 12,
                }}>
                  <View style={{
                    width: 12,
                    height: 12,
                    borderRadius: 6,
                    backgroundColor: getDemandColor(selectedArea.demandLevel),
                    marginRight: 8,
                  }} />
                  <Text style={{
                    fontSize: 18,
                    fontWeight: '600',
                    color: getDemandColor(selectedArea.demandLevel),
                  }}>
                    {getDemandLabel(selectedArea.demandLevel)} Demand
                  </Text>
                </View>

                {selectedArea.demandLevel === DemandLevel.SURGE && (
                  <View style={{
                    backgroundColor: '#fef3c7',
                    padding: 12,
                    borderRadius: 8,
                    marginBottom: 12,
                  }}>
                    <Text style={{
                      fontSize: 16,
                      fontWeight: '600',
                      color: '#d97706',
                      marginBottom: 4,
                    }}>
                      🔥 SURGE PRICING ACTIVE
                    </Text>
                    <Text style={{
                      fontSize: 14,
                      color: '#92400e',
                    }}>
                      Earn {selectedArea.surgeMultiplier}x more per delivery + Rs.{selectedArea.bonusAmount} bonus
                    </Text>
                  </View>
                )}

                <View style={{
                  flexDirection: 'row',
                  justifyContent: 'space-between',
                  paddingTop: 12,
                  borderTopWidth: 1,
                  borderTopColor: '#f3f4f6',
                }}>
                  <View style={{ alignItems: 'center', flex: 1 }}>
                    <Text style={{
                      fontSize: 18,
                      fontWeight: 'bold',
                      color: '#1f2937',
                    }}>
                      {selectedArea.estimatedOrders}
                    </Text>
                    <Text style={{
                      fontSize: 12,
                      color: '#6b7280',
                      textAlign: 'center',
                    }}>
                      Est. Orders
                    </Text>
                  </View>

                  <View style={{ alignItems: 'center', flex: 1 }}>
                    <Text style={{
                      fontSize: 18,
                      fontWeight: 'bold',
                      color: '#1f2937',
                    }}>
                      Rs.{selectedArea.averageEarnings}
                    </Text>
                    <Text style={{
                      fontSize: 12,
                      color: '#6b7280',
                      textAlign: 'center',
                    }}>
                      Avg Earnings
                    </Text>
                  </View>

                  <View style={{ alignItems: 'center', flex: 1 }}>
                    <Text style={{
                      fontSize: 18,
                      fontWeight: 'bold',
                      color: '#1f2937',
                    }}>
                      {selectedArea.activeRiders}
                    </Text>
                    <Text style={{
                      fontSize: 12,
                      color: '#6b7280',
                      textAlign: 'center',
                    }}>
                      Active Riders
                    </Text>
                  </View>
                </View>
              </View>

              {/* Peak Hours */}
              <View style={{
                backgroundColor: 'white',
                borderRadius: 12,
                padding: 16,
                marginBottom: 16,
              }}>
                <Text style={{
                  fontSize: 16,
                  fontWeight: '600',
                  color: '#1f2937',
                  marginBottom: 12,
                }}>
                  Peak Hours
                </Text>

                {selectedArea.peakHours.map((peak, index) => (
                  <View
                    key={index}
                    style={{
                      flexDirection: 'row',
                      alignItems: 'center',
                      paddingVertical: 8,
                      paddingHorizontal: 12,
                      backgroundColor: '#f0f9ff',
                      borderRadius: 8,
                      marginBottom: 8,
                    }}
                  >
                    <Ionicons name="time" size={16} color="#0284c7" />
                    <Text style={{
                      fontSize: 14,
                      color: '#0369a1',
                      marginLeft: 8,
                      fontWeight: '500',
                    }}>
                      {peak.start} - {peak.end}
                    </Text>
                  </View>
                ))}
              </View>

              {/* Area Info */}
              <View style={{
                backgroundColor: 'white',
                borderRadius: 12,
                padding: 16,
                marginBottom: 32,
              }}>
                <Text style={{
                  fontSize: 16,
                  fontWeight: '600',
                  color: '#1f2937',
                  marginBottom: 12,
                }}>
                  Area Information
                </Text>

                <View style={{
                  flexDirection: 'row',
                  justifyContent: 'space-between',
                  marginBottom: 12,
                }}>
                  <Text style={{
                    fontSize: 14,
                    color: '#6b7280',
                  }}>
                    Area Type
                  </Text>
                  <Text style={{
                    fontSize: 14,
                    color: '#1f2937',
                    fontWeight: '500',
                  }}>
                    {selectedArea.areaType.replace('_', ' ').replace(/\b\w/g, l => l.toUpperCase())}
                  </Text>
                </View>

                <View style={{
                  flexDirection: 'row',
                  justifyContent: 'space-between',
                  marginBottom: 12,
                }}>
                  <Text style={{
                    fontSize: 14,
                    color: '#6b7280',
                  }}>
                    Coverage Radius
                  </Text>
                  <Text style={{
                    fontSize: 14,
                    color: '#1f2937',
                    fontWeight: '500',
                  }}>
                    {selectedArea.radius} meters
                  </Text>
                </View>

                <View style={{
                  flexDirection: 'row',
                  justifyContent: 'space-between',
                  marginBottom: 12,
                }}>
                  <Text style={{
                    fontSize: 14,
                    color: '#6b7280',
                  }}>
                    Avg Delivery Time
                  </Text>
                  <Text style={{
                    fontSize: 14,
                    color: '#1f2937',
                    fontWeight: '500',
                  }}>
                    {selectedArea.averageDeliveryTime} minutes
                  </Text>
                </View>

                <View style={{
                  flexDirection: 'row',
                  justifyContent: 'space-between',
                }}>
                  <Text style={{
                    fontSize: 14,
                    color: '#6b7280',
                  }}>
                    Last Updated
                  </Text>
                  <Text style={{
                    fontSize: 14,
                    color: '#1f2937',
                    fontWeight: '500',
                  }}>
                    {new Date(selectedArea.lastUpdated).toLocaleTimeString()}
                  </Text>
                </View>
              </View>
            </ScrollView>
          </SafeAreaView>
        )}
      </Modal>
    </SafeAreaView>
  );
};

export default HeatmapScreen;