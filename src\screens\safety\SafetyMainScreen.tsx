import React, { useState, useEffect } from 'react';
import {
  View,
  Text,
  TouchableOpacity,
  ScrollView,
  Alert,
  Linking,
} from 'react-native';
import { SafeAreaView } from 'react-native-safe-area-context';
import { Ionicons } from '@expo/vector-icons';
import { useNavigation } from '@react-navigation/native';
import { StackNavigationProp } from '@react-navigation/stack';
import { SafetyStackParamList } from '../../types';
import { LocationSharingStatus } from '../../types/safety';
import { makeCall, getEmergencyNumbers } from '../../utils/phoneUtils';

type SafetyMainScreenNavigationProp = StackNavigationProp<SafetyStackParamList, 'SafetyMain'>;

const SafetyMainScreen: React.FC = () => {
  const navigation = useNavigation<SafetyMainScreenNavigationProp>();
  const [locationSharing, setLocationSharing] = useState<LocationSharingStatus>(LocationSharingStatus.INACTIVE);
  const [recentIncidents, setRecentIncidents] = useState(2);
  const [pendingDocuments, setPendingDocuments] = useState(1);

  const safetyOptions = [
    {
      id: 'emergency-sos',
      title: 'Emergency SOS',
      description: 'Quick access to emergency services and location sharing',
      icon: 'warning-outline',
      color: '#dc2626',
      onPress: () => navigation.navigate('EmergencySOS'),
      urgent: true,
    },
    {
      id: 'incident-report',
      title: 'Report Incident',
      description: 'Log accidents, issues, or safety concerns during delivery',
      icon: 'document-text',
      color: '#ea580c',
      onPress: () => navigation.navigate('IncidentReport'),
      urgent: false,
    },
    {
      id: 'terms-policies',
      title: 'Terms & Policies',
      description: 'Read legal documents, privacy policy, and rider agreement',
      icon: 'library-outline',
      color: '#0891b2',
      onPress: () => navigation.navigate('TermsPolicies'),
      urgent: false,
      badge: pendingDocuments > 0 ? pendingDocuments : undefined,
    },
  ];

  const quickActions = [
    {
      id: 'call-police',
      title: 'Police',
      number: '15',
      icon: 'shield-outline',
      color: '#dc2626',
    },
    {
      id: 'call-ambulance',
      title: 'Ambulance',
      number: '1122',
      icon: 'medical-outline',
      color: '#dc2626',
    },
    {
      id: 'call-support',
      title: 'FoodWay Support',
      number: '+92-21-111-FOOD',
      icon: 'headset-outline',
      color: '#10b981',
    },
    {
      id: 'call-roadside',
      title: 'Roadside Assistance',
      number: '+92-21-111-HELP',
      icon: 'construct-outline',
      color: '#f59e0b',
    },
  ];

  const callNumber = async (phoneNumber: string, title: string) => {
    await makeCall({
      phoneNumber,
      contactName: title,
      showConfirmation: true,
      fallbackMessage: `Unable to call ${title}. Please dial manually.`
    });
  };

  const toggleLocationSharing = () => {
    const newStatus = locationSharing === LocationSharingStatus.ACTIVE 
      ? LocationSharingStatus.INACTIVE 
      : LocationSharingStatus.ACTIVE;
    
    setLocationSharing(newStatus);
    
    Alert.alert(
      'Location Sharing',
      newStatus === LocationSharingStatus.ACTIVE 
        ? 'Your location is now being shared with emergency contacts.'
        : 'Location sharing has been stopped.'
    );
  };

  const renderSafetyOption = (option: typeof safetyOptions[0]) => (
    <TouchableOpacity
      key={option.id}
      onPress={option.onPress}
      style={{
        backgroundColor: 'white',
        marginHorizontal: 16,
        marginVertical: 8,
        borderRadius: 12,
        padding: 16,
        flexDirection: 'row',
        alignItems: 'center',
        borderWidth: option.urgent ? 2 : 1,
        borderColor: option.urgent ? option.color : '#e5e7eb',
        shadowColor: option.color,
        shadowOffset: { width: 0, height: 2 },
        shadowOpacity: 0.1,
        shadowRadius: 4,
        elevation: 3,
      }}
    >
      <View style={{
        width: 48,
        height: 48,
        backgroundColor: `${option.color}15`,
        borderRadius: 24,
        alignItems: 'center',
        justifyContent: 'center',
        marginRight: 16,
        position: 'relative',
      }}>
        <Ionicons
          name={option.icon as any}
          size={24}
          color={option.color}
        />
        {option.badge && (
          <View style={{
            position: 'absolute',
            top: -4,
            right: -4,
            backgroundColor: '#dc2626',
            borderRadius: 10,
            width: 20,
            height: 20,
            alignItems: 'center',
            justifyContent: 'center',
          }}>
            <Text style={{
              color: 'white',
              fontSize: 12,
              fontWeight: 'bold',
            }}>
              {option.badge}
            </Text>
          </View>
        )}
      </View>
      
      <View style={{ flex: 1 }}>
        <Text style={{
          fontSize: 16,
          fontWeight: '600',
          color: '#1f2937',
          marginBottom: 4,
        }}>
          {option.title}
        </Text>
        <Text style={{
          fontSize: 14,
          color: '#6b7280',
          lineHeight: 20,
        }}>
          {option.description}
        </Text>
      </View>
      
      <Ionicons
        name="chevron-forward"
        size={20}
        color={option.color}
      />
    </TouchableOpacity>
  );

  const renderQuickAction = (action: typeof quickActions[0]) => (
    <TouchableOpacity
      key={action.id}
      onPress={() => callNumber(action.number, action.title)}
      style={{
        backgroundColor: 'white',
        borderRadius: 12,
        padding: 16,
        alignItems: 'center',
        borderWidth: 1,
        borderColor: '#e5e7eb',
        flex: 1,
        marginHorizontal: 4,
        shadowColor: '#000',
        shadowOffset: { width: 0, height: 1 },
        shadowOpacity: 0.1,
        shadowRadius: 2,
        elevation: 2,
      }}
    >
      <View style={{
        width: 40,
        height: 40,
        backgroundColor: `${action.color}15`,
        borderRadius: 20,
        alignItems: 'center',
        justifyContent: 'center',
        marginBottom: 8,
      }}>
        <Ionicons
          name={action.icon as any}
          size={20}
          color={action.color}
        />
      </View>
      <Text style={{
        fontSize: 12,
        fontWeight: '600',
        color: '#1f2937',
        textAlign: 'center',
        marginBottom: 4,
      }}>
        {action.title}
      </Text>
      <Text style={{
        fontSize: 10,
        color: '#6b7280',
        textAlign: 'center',
      }}>
        {action.number}
      </Text>
    </TouchableOpacity>
  );

  return (
    <SafeAreaView style={{ flex: 1, backgroundColor: '#f9fafb' }}>
      {/* Header */}
      <View style={{
        flexDirection: 'row',
        alignItems: 'center',
        paddingHorizontal: 16,
        paddingVertical: 12,
        backgroundColor: 'white',
        borderBottomWidth: 1,
        borderBottomColor: '#e5e7eb',
      }}>
        <Text style={{
          fontSize: 20,
          fontWeight: '700',
          color: '#1f2937',
          flex: 1,
        }}>
          Safety & Legal
        </Text>
        
        <TouchableOpacity
          onPress={toggleLocationSharing}
          style={{
            flexDirection: 'row',
            alignItems: 'center',
            backgroundColor: locationSharing === LocationSharingStatus.ACTIVE ? '#10b98115' : '#f3f4f6',
            paddingHorizontal: 12,
            paddingVertical: 6,
            borderRadius: 16,
          }}
        >
          <Ionicons
            name="location"
            size={16}
            color={locationSharing === LocationSharingStatus.ACTIVE ? '#10b981' : '#6b7280'}
          />
          <Text style={{
            fontSize: 12,
            color: locationSharing === LocationSharingStatus.ACTIVE ? '#10b981' : '#6b7280',
            fontWeight: '500',
            marginLeft: 4,
          }}>
            {locationSharing === LocationSharingStatus.ACTIVE ? 'Sharing' : 'Share Location'}
          </Text>
        </TouchableOpacity>
      </View>

      <ScrollView style={{ flex: 1 }}>
        {/* Safety Status */}
        <View style={{
          backgroundColor: 'white',
          marginHorizontal: 16,
          marginTop: 16,
          borderRadius: 12,
          padding: 16,
          borderWidth: 1,
          borderColor: '#e5e7eb',
        }}>
          <View style={{
            flexDirection: 'row',
            alignItems: 'center',
            marginBottom: 16,
          }}>
            <Ionicons name="shield-checkmark" size={24} color="#10b981" />
            <Text style={{
              fontSize: 18,
              fontWeight: '600',
              color: '#1f2937',
              marginLeft: 8,
            }}>
              Safety Status
            </Text>
          </View>
          
          <View style={{
            flexDirection: 'row',
            justifyContent: 'space-between',
          }}>
            <View style={{ alignItems: 'center', flex: 1 }}>
              <Text style={{
                fontSize: 24,
                fontWeight: 'bold',
                color: locationSharing === LocationSharingStatus.ACTIVE ? '#10b981' : '#6b7280',
              }}>
                {locationSharing === LocationSharingStatus.ACTIVE ? 'ON' : 'OFF'}
              </Text>
              <Text style={{
                fontSize: 12,
                color: '#6b7280',
                textAlign: 'center',
              }}>
                Location Sharing
              </Text>
            </View>
            
            <View style={{ alignItems: 'center', flex: 1 }}>
              <Text style={{
                fontSize: 24,
                fontWeight: 'bold',
                color: recentIncidents === 0 ? '#10b981' : '#f59e0b',
              }}>
                {recentIncidents}
              </Text>
              <Text style={{
                fontSize: 12,
                color: '#6b7280',
                textAlign: 'center',
              }}>
                Recent Incidents
              </Text>
            </View>
            
            <View style={{ alignItems: 'center', flex: 1 }}>
              <Text style={{
                fontSize: 24,
                fontWeight: 'bold',
                color: pendingDocuments === 0 ? '#10b981' : '#dc2626',
              }}>
                {pendingDocuments}
              </Text>
              <Text style={{
                fontSize: 12,
                color: '#6b7280',
                textAlign: 'center',
              }}>
                Pending Documents
              </Text>
            </View>
          </View>
        </View>

        {/* Quick Emergency Contacts */}
        <View style={{ marginTop: 24 }}>
          <Text style={{
            fontSize: 18,
            fontWeight: '600',
            color: '#1f2937',
            paddingHorizontal: 16,
            marginBottom: 12,
          }}>
            Emergency Contacts
          </Text>
          
          <View style={{
            flexDirection: 'row',
            paddingHorizontal: 12,
          }}>
            {quickActions.map(action => renderQuickAction(action))}
          </View>
        </View>

        {/* Safety Features */}
        <View style={{ marginTop: 32 }}>
          <Text style={{
            fontSize: 18,
            fontWeight: '600',
            color: '#1f2937',
            paddingHorizontal: 16,
            marginBottom: 12,
          }}>
            Safety & Legal Features
          </Text>
          
          {safetyOptions.map(option => renderSafetyOption(option))}
        </View>

        {/* Safety Tips */}
        <View style={{
          backgroundColor: '#f0f9ff',
          marginHorizontal: 16,
          marginTop: 24,
          marginBottom: 32,
          borderRadius: 12,
          padding: 16,
          borderWidth: 1,
          borderColor: '#bae6fd',
        }}>
          <View style={{
            flexDirection: 'row',
            alignItems: 'center',
            marginBottom: 12,
          }}>
            <Ionicons name="bulb" size={20} color="#0284c7" />
            <Text style={{
              fontSize: 16,
              fontWeight: '600',
              color: '#0284c7',
              marginLeft: 8,
            }}>
              Safety Tips
            </Text>
          </View>
          
          <Text style={{
            fontSize: 14,
            color: '#0369a1',
            lineHeight: 20,
            marginBottom: 8,
          }}>
            • Always inform someone about your delivery route
          </Text>
          <Text style={{
            fontSize: 14,
            color: '#0369a1',
            lineHeight: 20,
            marginBottom: 8,
          }}>
            • Keep your phone charged and accessible
          </Text>
          <Text style={{
            fontSize: 14,
            color: '#0369a1',
            lineHeight: 20,
            marginBottom: 8,
          }}>
            • Trust your instincts and avoid unsafe situations
          </Text>
          <Text style={{
            fontSize: 14,
            color: '#0369a1',
            lineHeight: 20,
          }}>
            • Report any incidents immediately
          </Text>
        </View>
      </ScrollView>
    </SafeAreaView>
  );
};

export default SafetyMainScreen;
