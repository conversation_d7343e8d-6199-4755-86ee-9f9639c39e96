import React, { useState } from 'react';
import { createStackNavigator } from '@react-navigation/stack';
import { ProfileStackParamList } from '../types';
import { DocumentStatus, DocumentVerificationStatus } from '../types/auth';

// Profile screens
import ProfileMainScreen from '../screens/profile/ProfileMainScreen';
import ProfileScreen from '../screens/profile/ProfileScreen';
import VehicleInfoScreen from '../screens/profile/VehicleInfoScreen';
import PaymentMethodsScreen from '../screens/profile/PaymentMethodsScreen';
import SettingsScreen from '../screens/profile/SettingsScreen';
import SettingsMainScreen from '../screens/settings/SettingsMainScreen';
import DocumentStatusScreen from '../screens/profile/DocumentStatusScreen';

const Stack = createStackNavigator<ProfileStackParamList>();

const ProfileNavigator: React.FC = () => {
  return (
    <Stack.Navigator
      id={"ProfileStack" as any}
      screenOptions={{
        headerShown: false,
      }}
    >
      <Stack.Screen name="ProfileMain" component={ProfileMainScreen} />
      <Stack.Screen name="EditProfile" component={ProfileScreen} />
      <Stack.Screen name="ProfileLegacy" component={ProfileScreen} />
      <Stack.Screen name="VehicleInfo" component={VehicleInfoScreen} />
      <Stack.Screen name="PaymentMethods" component={PaymentMethodsScreen} />
      <Stack.Screen name="BankInfo" component={PaymentMethodsScreen} />
      <Stack.Screen name="Settings" component={SettingsMainScreen} />
      <Stack.Screen name="SettingsLegacy" component={SettingsScreen} />
      <Stack.Screen name="DocumentStatus" component={DocumentStatusScreen} />
    </Stack.Navigator>
  );
};

// DocumentStatus wrapper for main app
const DocumentStatusScreenWrapper: React.FC<any> = ({ navigation }) => {
  // Mock document status - in real app, this would come from user context/API
  const [documentStatus] = useState<DocumentStatus>({
    cnic: DocumentVerificationStatus.PENDING,
    passport: DocumentVerificationStatus.NOT_SUBMITTED,
    drivingLicense: DocumentVerificationStatus.APPROVED,
    vehicleRegistration: DocumentVerificationStatus.REJECTED,
    profilePhoto: DocumentVerificationStatus.PENDING,
    overall: DocumentVerificationStatus.PENDING,
    rejectionReasons: ['Vehicle registration document is not clear', 'Please upload a clearer image'],
    lastUpdated: new Date().toISOString(),
  });

  const handleUploadDocument = (documentType: string) => {
    // In main app, this could navigate to a document upload screen
    // For now, we'll show an alert
    console.log('Upload document:', documentType);
    // You could navigate to a document upload screen here
    // navigation.navigate('DocumentUpload', { documentType });
  };

  const handleRefresh = async () => {
    console.log('Refreshing document status...');
    // Implement refresh logic - fetch latest status from API
  };

  const handleContinue = () => {
    // When documents are approved, user can continue using the app
    console.log('Documents approved, continuing...');
    navigation.goBack();
  };

  return (
    <DocumentStatusScreen
      documentStatus={documentStatus}
      onUploadDocument={handleUploadDocument}
      onRefresh={handleRefresh}
      onContinue={handleContinue}
      isLoading={false}
    />
  );
};

export default ProfileNavigator;
