import React, { useState, useEffect } from 'react';
import {
  View,
  Text,
  TouchableOpacity,
  ScrollView,
  Modal,
  FlatList,
  Dimensions,
  Alert,
} from 'react-native';
import { SafeAreaView } from 'react-native-safe-area-context';
import { Ionicons } from '@expo/vector-icons';
import { useNavigation } from '@react-navigation/native';
import { StackNavigationProp } from '@react-navigation/stack';
import { AdvancedToolsStackParamList } from '../../types';
import {
  TrainingVideo,
  TrainingCategory,
  VideoProgress,
  TrainingCertificate,
  VideoRating,
} from '../../types/advancedTools';

type TrainingScreenNavigationProp = StackNavigationProp<AdvancedToolsStackParamList, 'Training'>;

const { width } = Dimensions.get('window');

const TrainingScreen: React.FC = () => {
  const navigation = useNavigation<TrainingScreenNavigationProp>();
  const [trainingVideos, setTrainingVideos] = useState<TrainingVideo[]>([]);
  const [videoProgress, setVideoProgress] = useState<VideoProgress[]>([]);
  const [certificates, setCertificates] = useState<TrainingCertificate[]>([]);
  const [selectedCategory, setSelectedCategory] = useState<TrainingCategory | 'all'>('all');
  const [selectedVideo, setSelectedVideo] = useState<TrainingVideo | null>(null);
  const [showVideoPlayer, setShowVideoPlayer] = useState(false);
  const [showCertificates, setShowCertificates] = useState(false);
  const [currentVideoTime, setCurrentVideoTime] = useState(0);
  const [isPlaying, setIsPlaying] = useState(false);
  const [loading, setLoading] = useState(true);

  // Mock training data
  const mockTrainingVideos: TrainingVideo[] = [
    {
      id: 'video-1',
      title: 'Safe Delivery Practices',
      description: 'Learn essential safety protocols for food delivery including traffic rules, vehicle safety, and personal protection.',
      category: TrainingCategory.SAFE_DELIVERY,
      duration: 480, // 8 minutes
      thumbnailUrl: undefined,
      videoUrl: 'https://example.com/video1.mp4',
      instructor: 'Safety Expert Ahmad Khan',
      difficulty: 'Beginner',
      tags: ['safety', 'traffic', 'protection'],
      isRequired: true,
      points: 100,
      createdAt: new Date(Date.now() - 30 * 24 * 60 * 60 * 1000).toISOString(),
      updatedAt: new Date(Date.now() - 30 * 24 * 60 * 60 * 1000).toISOString(),
    },
    {
      id: 'video-2',
      title: 'FoodWay App Navigation',
      description: 'Complete guide to using the FoodWay rider app including order management, navigation, and customer communication.',
      category: TrainingCategory.APP_USAGE,
      duration: 600, // 10 minutes
      thumbnailUrl: undefined,
      videoUrl: 'https://example.com/video2.mp4',
      instructor: 'Tech Trainer Sarah Ahmed',
      difficulty: 'Beginner',
      tags: ['app', 'navigation', 'orders'],
      isRequired: true,
      points: 120,
      createdAt: new Date(Date.now() - 25 * 24 * 60 * 60 * 1000).toISOString(),
      updatedAt: new Date(Date.now() - 25 * 24 * 60 * 60 * 1000).toISOString(),
    },
    {
      id: 'video-3',
      title: 'Customer Service Excellence',
      description: 'Master customer interaction skills, handling complaints, and providing exceptional service experience.',
      category: TrainingCategory.CUSTOMER_SERVICE,
      duration: 720, // 12 minutes
      thumbnailUrl: undefined,
      videoUrl: 'https://example.com/video3.mp4',
      instructor: 'Service Manager Fatima Ali',
      difficulty: 'Intermediate',
      tags: ['customer', 'service', 'communication'],
      isRequired: false,
      points: 150,
      createdAt: new Date(Date.now() - 20 * 24 * 60 * 60 * 1000).toISOString(),
      updatedAt: new Date(Date.now() - 20 * 24 * 60 * 60 * 1000).toISOString(),
    },
    {
      id: 'video-4',
      title: 'Motorcycle Maintenance Basics',
      description: 'Essential motorcycle maintenance tips to keep your vehicle in optimal condition and prevent breakdowns.',
      category: TrainingCategory.VEHICLE_MAINTENANCE,
      duration: 900, // 15 minutes
      thumbnailUrl: undefined,
      videoUrl: 'https://example.com/video4.mp4',
      instructor: 'Mechanic Expert Usman Shah',
      difficulty: 'Intermediate',
      tags: ['maintenance', 'motorcycle', 'repair'],
      isRequired: false,
      points: 180,
      createdAt: new Date(Date.now() - 15 * 24 * 60 * 60 * 1000).toISOString(),
      updatedAt: new Date(Date.now() - 15 * 24 * 60 * 60 * 1000).toISOString(),
    },
    {
      id: 'video-5',
      title: 'Emergency Response Procedures',
      description: 'Critical emergency response protocols including accident procedures, first aid basics, and emergency contacts.',
      category: TrainingCategory.EMERGENCY_PROCEDURES,
      duration: 540, // 9 minutes
      thumbnailUrl: undefined,
      videoUrl: 'https://example.com/video5.mp4',
      instructor: 'Emergency Specialist Dr. Hassan',
      difficulty: 'Advanced',
      tags: ['emergency', 'first-aid', 'safety'],
      isRequired: true,
      points: 200,
      createdAt: new Date(Date.now() - 10 * 24 * 60 * 60 * 1000).toISOString(),
      updatedAt: new Date(Date.now() - 10 * 24 * 60 * 60 * 1000).toISOString(),
    },
    {
      id: 'video-6',
      title: 'Food Safety & Hygiene',
      description: 'Food safety protocols, hygiene standards, and proper food handling during delivery to ensure customer health.',
      category: TrainingCategory.FOOD_SAFETY,
      duration: 420, // 7 minutes
      thumbnailUrl: undefined,
      videoUrl: 'https://example.com/video6.mp4',
      instructor: 'Health Inspector Aisha Khan',
      difficulty: 'Beginner',
      tags: ['food-safety', 'hygiene', 'health'],
      isRequired: true,
      points: 130,
      createdAt: new Date(Date.now() - 8 * 24 * 60 * 60 * 1000).toISOString(),
      updatedAt: new Date(Date.now() - 8 * 24 * 60 * 60 * 1000).toISOString(),
    },
    {
      id: 'video-7',
      title: 'Traffic Rules & Road Safety',
      description: 'Comprehensive guide to Pakistani traffic laws, road signs, and safe driving practices for delivery riders.',
      category: TrainingCategory.TRAFFIC_RULES,
      duration: 660, // 11 minutes
      thumbnailUrl: undefined,
      videoUrl: 'https://example.com/video7.mp4',
      instructor: 'Traffic Police Officer Malik',
      difficulty: 'Intermediate',
      tags: ['traffic', 'rules', 'safety'],
      isRequired: true,
      points: 160,
      createdAt: new Date(Date.now() - 5 * 24 * 60 * 60 * 1000).toISOString(),
      updatedAt: new Date(Date.now() - 5 * 24 * 60 * 60 * 1000).toISOString(),
    },
    {
      id: 'video-8',
      title: 'Maximizing Your Earnings',
      description: 'Strategic tips for optimizing delivery routes, peak hour strategies, and bonus maximization techniques.',
      category: TrainingCategory.EARNINGS_OPTIMIZATION,
      duration: 780, // 13 minutes
      thumbnailUrl: undefined,
      videoUrl: 'https://example.com/video8.mp4',
      instructor: 'Top Rider Muhammad Ali',
      difficulty: 'Advanced',
      tags: ['earnings', 'strategy', 'optimization'],
      isRequired: false,
      points: 220,
      createdAt: new Date(Date.now() - 3 * 24 * 60 * 60 * 1000).toISOString(),
      updatedAt: new Date(Date.now() - 3 * 24 * 60 * 60 * 1000).toISOString(),
    },
  ];

  const mockVideoProgress: VideoProgress[] = [
    {
      id: 'progress-1',
      riderId: 'rider-1',
      videoId: 'video-1',
      watchedDuration: 480,
      totalDuration: 480,
      isCompleted: true,
      completedAt: new Date(Date.now() - 7 * 24 * 60 * 60 * 1000).toISOString(),
      lastWatchedAt: new Date(Date.now() - 7 * 24 * 60 * 60 * 1000).toISOString(),
      rating: {
        score: 5,
        feedback: 'Very helpful and informative!',
        createdAt: new Date(Date.now() - 7 * 24 * 60 * 60 * 1000).toISOString(),
      },
    },
    {
      id: 'progress-2',
      riderId: 'rider-1',
      videoId: 'video-2',
      watchedDuration: 600,
      totalDuration: 600,
      isCompleted: true,
      completedAt: new Date(Date.now() - 5 * 24 * 60 * 60 * 1000).toISOString(),
      lastWatchedAt: new Date(Date.now() - 5 * 24 * 60 * 60 * 1000).toISOString(),
      rating: {
        score: 4,
        feedback: 'Good overview of the app features.',
        createdAt: new Date(Date.now() - 5 * 24 * 60 * 60 * 1000).toISOString(),
      },
    },
    {
      id: 'progress-3',
      riderId: 'rider-1',
      videoId: 'video-3',
      watchedDuration: 360,
      totalDuration: 720,
      isCompleted: false,
      completedAt: '',
      lastWatchedAt: new Date(Date.now() - 2 * 24 * 60 * 60 * 1000).toISOString(),
    },
    {
      id: 'progress-6',
      riderId: 'rider-1',
      videoId: 'video-6',
      watchedDuration: 420,
      totalDuration: 420,
      isCompleted: true,
      completedAt: new Date(Date.now() - 3 * 24 * 60 * 60 * 1000).toISOString(),
      lastWatchedAt: new Date(Date.now() - 3 * 24 * 60 * 60 * 1000).toISOString(),
      rating: {
        score: 5,
        feedback: 'Essential knowledge for food delivery!',
        createdAt: new Date(Date.now() - 3 * 24 * 60 * 60 * 1000).toISOString(),
      },
    },
  ];

  const mockCertificates: TrainingCertificate[] = [
    {
      id: 'cert-1',
      riderId: 'rider-1',
      videoId: 'video-1',
      video: mockTrainingVideos[0],
      certificateNumber: 'FW-CERT-001-2024',
      issuedAt: new Date(Date.now() - 7 * 24 * 60 * 60 * 1000).toISOString(),
      expiresAt: new Date(Date.now() + 358 * 24 * 60 * 60 * 1000).toISOString(), // 1 year
      score: 100,
      isValid: true,
    },
    {
      id: 'cert-2',
      riderId: 'rider-1',
      videoId: 'video-2',
      video: mockTrainingVideos[1],
      certificateNumber: 'FW-CERT-002-2024',
      issuedAt: new Date(Date.now() - 5 * 24 * 60 * 60 * 1000).toISOString(),
      expiresAt: new Date(Date.now() + 360 * 24 * 60 * 60 * 1000).toISOString(),
      score: 95,
      isValid: true,
    },
    {
      id: 'cert-6',
      riderId: 'rider-1',
      videoId: 'video-6',
      video: mockTrainingVideos[5],
      certificateNumber: 'FW-CERT-006-2024',
      issuedAt: new Date(Date.now() - 3 * 24 * 60 * 60 * 1000).toISOString(),
      expiresAt: new Date(Date.now() + 362 * 24 * 60 * 60 * 1000).toISOString(),
      score: 100,
      isValid: true,
    },
  ];

  useEffect(() => {
    loadTrainingData();
  }, []);

  const loadTrainingData = async () => {
    setLoading(true);
    try {
      // Simulate API call
      await new Promise(resolve => setTimeout(resolve, 1000));
      setTrainingVideos(mockTrainingVideos);
      setVideoProgress(mockVideoProgress);
      setCertificates(mockCertificates);
    } catch (error) {
      console.error('Error loading training data:', error);
    } finally {
      setLoading(false);
    }
  };

  const getVideoProgress = (videoId: string): VideoProgress | undefined => {
    return videoProgress.find(p => p.videoId === videoId);
  };

  const getProgressPercentage = (videoId: string): number => {
    const progress = getVideoProgress(videoId);
    if (!progress) return 0;
    return (progress.watchedDuration / progress.totalDuration) * 100;
  };

  const isVideoCompleted = (videoId: string): boolean => {
    const progress = getVideoProgress(videoId);
    return progress?.isCompleted || false;
  };

  const formatDuration = (seconds: number): string => {
    const minutes = Math.floor(seconds / 60);
    const remainingSeconds = seconds % 60;
    return `${minutes}:${remainingSeconds.toString().padStart(2, '0')}`;
  };

  const getCategoryIcon = (category: TrainingCategory): string => {
    switch (category) {
      case TrainingCategory.SAFE_DELIVERY:
        return 'shield-checkmark';
      case TrainingCategory.APP_USAGE:
        return 'phone-portrait';
      case TrainingCategory.CUSTOMER_SERVICE:
        return 'people';
      case TrainingCategory.VEHICLE_MAINTENANCE:
        return 'build';
      case TrainingCategory.EMERGENCY_PROCEDURES:
        return 'medical';
      case TrainingCategory.FOOD_SAFETY:
        return 'restaurant';
      case TrainingCategory.TRAFFIC_RULES:
        return 'car';
      case TrainingCategory.EARNINGS_OPTIMIZATION:
        return 'trending-up';
      default:
        return 'play-circle';
    }
  };

  const getCategoryColor = (category: TrainingCategory): string => {
    switch (category) {
      case TrainingCategory.SAFE_DELIVERY:
        return '#10b981';
      case TrainingCategory.APP_USAGE:
        return '#3b82f6';
      case TrainingCategory.CUSTOMER_SERVICE:
        return '#8b5cf6';
      case TrainingCategory.VEHICLE_MAINTENANCE:
        return '#f59e0b';
      case TrainingCategory.EMERGENCY_PROCEDURES:
        return '#ef4444';
      case TrainingCategory.FOOD_SAFETY:
        return '#06b6d4';
      case TrainingCategory.TRAFFIC_RULES:
        return '#84cc16';
      case TrainingCategory.EARNINGS_OPTIMIZATION:
        return '#f97316';
      default:
        return '#6b7280';
    }
  };

  const getDifficultyColor = (difficulty: string): string => {
    switch (difficulty.toLowerCase()) {
      case 'beginner':
        return '#10b981';
      case 'intermediate':
        return '#f59e0b';
      case 'advanced':
        return '#ef4444';
      default:
        return '#6b7280';
    }
  };

  const filteredVideos = selectedCategory === 'all' 
    ? trainingVideos 
    : trainingVideos.filter(video => video.category === selectedCategory);

  const completedVideos = trainingVideos.filter(video => isVideoCompleted(video.id));
  const totalPoints = completedVideos.reduce((sum, video) => sum + video.points, 0);
  const requiredVideos = trainingVideos.filter(video => video.isRequired);
  const completedRequiredVideos = requiredVideos.filter(video => isVideoCompleted(video.id));

  const categories = Object.values(TrainingCategory);

  const startVideo = (video: TrainingVideo) => {
    setSelectedVideo(video);
    setCurrentVideoTime(getVideoProgress(video.id)?.watchedDuration || 0);
    setShowVideoPlayer(true);
  };

  const completeVideo = async (videoId: string, rating?: number, feedback?: string) => {
    try {
      // Update video progress
      const updatedProgress = videoProgress.map(p =>
        p.videoId === videoId
          ? {
              ...p,
              isCompleted: true,
              completedAt: new Date().toISOString(),
              rating: rating ? { score: rating, feedback: feedback || '', createdAt: new Date().toISOString() } : p.rating
            }
          : p
      );

      // Add new progress if doesn't exist
      if (!videoProgress.find(p => p.videoId === videoId)) {
        const video = trainingVideos.find(v => v.id === videoId);
        if (video) {
          updatedProgress.push({
            id: `progress-${Date.now()}`,
            riderId: 'rider-1',
            videoId,
            watchedDuration: video.duration,
            totalDuration: video.duration,
            isCompleted: true,
            completedAt: new Date().toISOString(),
            lastWatchedAt: new Date().toISOString(),
            rating: rating ? { score: rating, feedback: feedback || '', createdAt: new Date().toISOString() } : undefined,
          });
        }
      }

      setVideoProgress(updatedProgress);

      // Generate certificate
      const video = trainingVideos.find(v => v.id === videoId);
      if (video && !certificates.find(c => c.videoId === videoId)) {
        const newCertificate: TrainingCertificate = {
          id: `cert-${Date.now()}`,
          riderId: 'rider-1',
          videoId,
          video,
          certificateNumber: `FW-CERT-${String(certificates.length + 1).padStart(3, '0')}-2024`,
          issuedAt: new Date().toISOString(),
          expiresAt: new Date(Date.now() + 365 * 24 * 60 * 60 * 1000).toISOString(),
          score: rating || 100,
          isValid: true,
        };
        setCertificates([...certificates, newCertificate]);

        Alert.alert(
          'Congratulations! 🎉',
          `You've completed "${video.title}" and earned ${video.points} points! Your certificate is now available.`,
          [{ text: 'View Certificate', onPress: () => setShowCertificates(true) }]
        );
      }

      setShowVideoPlayer(false);
    } catch (error) {
      console.error('Error completing video:', error);
    }
  };

  const renderVideoCard = ({ item: video }: { item: TrainingVideo }) => {
    const progress = getProgressPercentage(video.id);
    const isCompleted = isVideoCompleted(video.id);
    const progressData = getVideoProgress(video.id);

    return (
      <TouchableOpacity
        onPress={() => startVideo(video)}
        style={{
          backgroundColor: 'white',
          borderRadius: 12,
          marginHorizontal: 16,
          marginBottom: 16,
          overflow: 'hidden',
          elevation: 2,
          shadowColor: '#000',
          shadowOffset: { width: 0, height: 2 },
          shadowOpacity: 0.1,
          shadowRadius: 4,
        }}
      >
        {/* Video Thumbnail */}
        <View style={{
          height: 120,
          backgroundColor: getCategoryColor(video.category) + '20',
          alignItems: 'center',
          justifyContent: 'center',
          position: 'relative',
        }}>
          <Ionicons
            name={getCategoryIcon(video.category) as any}
            size={40}
            color={getCategoryColor(video.category)}
          />

          {/* Play Button */}
          <View style={{
            position: 'absolute',
            bottom: 8,
            right: 8,
            width: 32,
            height: 32,
            borderRadius: 16,
            backgroundColor: 'rgba(0,0,0,0.7)',
            alignItems: 'center',
            justifyContent: 'center',
          }}>
            <Ionicons
              name={isCompleted ? 'checkmark' : 'play'}
              size={16}
              color="white"
            />
          </View>

          {/* Required Badge */}
          {video.isRequired && (
            <View style={{
              position: 'absolute',
              top: 8,
              left: 8,
              backgroundColor: '#ef4444',
              paddingHorizontal: 6,
              paddingVertical: 2,
              borderRadius: 8,
            }}>
              <Text style={{
                fontSize: 10,
                color: 'white',
                fontWeight: '600',
              }}>
                REQUIRED
              </Text>
            </View>
          )}

          {/* Duration */}
          <View style={{
            position: 'absolute',
            top: 8,
            right: 8,
            backgroundColor: 'rgba(0,0,0,0.7)',
            paddingHorizontal: 6,
            paddingVertical: 2,
            borderRadius: 8,
          }}>
            <Text style={{
              fontSize: 10,
              color: 'white',
              fontWeight: '600',
            }}>
              {formatDuration(video.duration)}
            </Text>
          </View>
        </View>

        {/* Video Info */}
        <View style={{ padding: 16 }}>
          <View style={{
            flexDirection: 'row',
            alignItems: 'center',
            marginBottom: 8,
          }}>
            <View style={{
              backgroundColor: getCategoryColor(video.category) + '20',
              paddingHorizontal: 8,
              paddingVertical: 2,
              borderRadius: 8,
              marginRight: 8,
            }}>
              <Text style={{
                fontSize: 10,
                color: getCategoryColor(video.category),
                fontWeight: '600',
                textTransform: 'uppercase',
              }}>
                {video.category.replace('_', ' ')}
              </Text>
            </View>

            <View style={{
              backgroundColor: getDifficultyColor(video.difficulty) + '20',
              paddingHorizontal: 8,
              paddingVertical: 2,
              borderRadius: 8,
            }}>
              <Text style={{
                fontSize: 10,
                color: getDifficultyColor(video.difficulty),
                fontWeight: '600',
              }}>
                {video.difficulty}
              </Text>
            </View>
          </View>

          <Text style={{
            fontSize: 16,
            fontWeight: '600',
            color: '#1f2937',
            marginBottom: 4,
          }}>
            {video.title}
          </Text>

          <Text style={{
            fontSize: 14,
            color: '#6b7280',
            lineHeight: 20,
            marginBottom: 8,
          }}>
            {video.description}
          </Text>

          <View style={{
            flexDirection: 'row',
            alignItems: 'center',
            justifyContent: 'space-between',
            marginBottom: 8,
          }}>
            <Text style={{
              fontSize: 12,
              color: '#6b7280',
            }}>
              By {video.instructor}
            </Text>

            <View style={{
              flexDirection: 'row',
              alignItems: 'center',
            }}>
              <Ionicons name="trophy" size={12} color="#f59e0b" />
              <Text style={{
                fontSize: 12,
                color: '#f59e0b',
                marginLeft: 4,
                fontWeight: '600',
              }}>
                {video.points} XP
              </Text>
            </View>
          </View>

          {/* Progress Bar */}
          {progress > 0 && (
            <View>
              <View style={{
                height: 4,
                backgroundColor: '#f3f4f6',
                borderRadius: 2,
                overflow: 'hidden',
                marginBottom: 4,
              }}>
                <View style={{
                  height: '100%',
                  width: `${progress}%`,
                  backgroundColor: isCompleted ? '#10b981' : getCategoryColor(video.category),
                }} />
              </View>

              <View style={{
                flexDirection: 'row',
                alignItems: 'center',
                justifyContent: 'space-between',
              }}>
                <Text style={{
                  fontSize: 12,
                  color: '#6b7280',
                }}>
                  {isCompleted ? 'Completed' : `${Math.round(progress)}% watched`}
                </Text>

                {isCompleted && progressData?.rating && (
                  <View style={{
                    flexDirection: 'row',
                    alignItems: 'center',
                  }}>
                    <Ionicons name="star" size={12} color="#fbbf24" />
                    <Text style={{
                      fontSize: 12,
                      color: '#fbbf24',
                      marginLeft: 2,
                      fontWeight: '600',
                    }}>
                      {progressData.rating.score}/5
                    </Text>
                  </View>
                )}
              </View>
            </View>
          )}
        </View>
      </TouchableOpacity>
    );
  };

  return (
    <SafeAreaView style={{ flex: 1, backgroundColor: '#f9fafb' }}>
      {/* Header */}
      <View style={{
        flexDirection: 'row',
        alignItems: 'center',
        paddingHorizontal: 16,
        paddingVertical: 12,
        backgroundColor: 'white',
        borderBottomWidth: 1,
        borderBottomColor: '#e5e7eb',
      }}>
        <TouchableOpacity
          onPress={() => navigation.goBack()}
          style={{
            width: 40,
            height: 40,
            alignItems: 'center',
            justifyContent: 'center',
            marginRight: 8,
          }}
        >
          <Ionicons name="arrow-back" size={24} color="#374151" />
        </TouchableOpacity>

        <Text style={{
          fontSize: 18,
          fontWeight: '600',
          color: '#1f2937',
          flex: 1,
        }}>
          Training Videos
        </Text>

        <TouchableOpacity
          onPress={() => setShowCertificates(true)}
          style={{
            width: 40,
            height: 40,
            alignItems: 'center',
            justifyContent: 'center',
            marginRight: 8,
          }}
        >
          <Ionicons name="ribbon" size={24} color="#374151" />
        </TouchableOpacity>

        <TouchableOpacity
          onPress={loadTrainingData}
          style={{
            width: 40,
            height: 40,
            alignItems: 'center',
            justifyContent: 'center',
          }}
        >
          <Ionicons name="refresh" size={24} color="#374151" />
        </TouchableOpacity>
      </View>

      {/* Progress Summary */}
      <View style={{
        backgroundColor: 'white',
        paddingHorizontal: 16,
        paddingVertical: 12,
        borderBottomWidth: 1,
        borderBottomColor: '#e5e7eb',
      }}>
        <View style={{
          flexDirection: 'row',
          justifyContent: 'space-between',
        }}>
          <View style={{ alignItems: 'center', flex: 1 }}>
            <Text style={{
              fontSize: 18,
              fontWeight: 'bold',
              color: '#10b981',
            }}>
              {completedVideos.length}
            </Text>
            <Text style={{
              fontSize: 12,
              color: '#6b7280',
              textAlign: 'center',
            }}>
              Completed
            </Text>
          </View>

          <View style={{ alignItems: 'center', flex: 1 }}>
            <Text style={{
              fontSize: 18,
              fontWeight: 'bold',
              color: '#f59e0b',
            }}>
              {totalPoints}
            </Text>
            <Text style={{
              fontSize: 12,
              color: '#6b7280',
              textAlign: 'center',
            }}>
              XP Earned
            </Text>
          </View>

          <View style={{ alignItems: 'center', flex: 1 }}>
            <Text style={{
              fontSize: 18,
              fontWeight: 'bold',
              color: completedRequiredVideos.length === requiredVideos.length ? '#10b981' : '#ef4444',
            }}>
              {completedRequiredVideos.length}/{requiredVideos.length}
            </Text>
            <Text style={{
              fontSize: 12,
              color: '#6b7280',
              textAlign: 'center',
            }}>
              Required
            </Text>
          </View>

          <View style={{ alignItems: 'center', flex: 1 }}>
            <Text style={{
              fontSize: 18,
              fontWeight: 'bold',
              color: '#8b5cf6',
            }}>
              {certificates.length}
            </Text>
            <Text style={{
              fontSize: 12,
              color: '#6b7280',
              textAlign: 'center',
            }}>
              Certificates
            </Text>
          </View>
        </View>
      </View>

      {/* Category Filter */}
      <ScrollView
        horizontal
        showsHorizontalScrollIndicator={false}
        style={{
          backgroundColor: 'white',
          borderBottomWidth: 1,
          borderBottomColor: '#e5e7eb',
        }}
        contentContainerStyle={{
          paddingHorizontal: 16,
          paddingVertical: 12,
        }}
      >
        <TouchableOpacity
          onPress={() => setSelectedCategory('all')}
          style={{
            paddingHorizontal: 16,
            paddingVertical: 8,
            borderRadius: 20,
            backgroundColor: selectedCategory === 'all' ? '#f97316' : '#f3f4f6',
            marginRight: 8,
          }}
        >
          <Text style={{
            fontSize: 14,
            fontWeight: '600',
            color: selectedCategory === 'all' ? 'white' : '#6b7280',
          }}>
            All Videos
          </Text>
        </TouchableOpacity>

        {categories.map((category) => (
          <TouchableOpacity
            key={category}
            onPress={() => setSelectedCategory(category)}
            style={{
              paddingHorizontal: 16,
              paddingVertical: 8,
              borderRadius: 20,
              backgroundColor: selectedCategory === category ? '#f97316' : '#f3f4f6',
              marginRight: 8,
            }}
          >
            <Text style={{
              fontSize: 14,
              fontWeight: '600',
              color: selectedCategory === category ? 'white' : '#6b7280',
            }}>
              {category.replace('_', ' ').replace(/\b\w/g, l => l.toUpperCase())}
            </Text>
          </TouchableOpacity>
        ))}
      </ScrollView>

      {/* Videos List */}
      {loading ? (
        <View style={{
          flex: 1,
          alignItems: 'center',
          justifyContent: 'center',
        }}>
          <Text style={{
            fontSize: 16,
            color: '#6b7280',
          }}>
            Loading training videos...
          </Text>
        </View>
      ) : (
        <FlatList
          data={filteredVideos}
          renderItem={renderVideoCard}
          keyExtractor={(item) => item.id}
          contentContainerStyle={{
            paddingVertical: 16,
          }}
          showsVerticalScrollIndicator={false}
        />
      )}

      {/* Video Player Modal */}
      <Modal
        visible={showVideoPlayer}
        animationType="slide"
        presentationStyle="fullScreen"
      >
        {selectedVideo && (
          <SafeAreaView style={{ flex: 1, backgroundColor: '#000' }}>
            {/* Video Player Header */}
            <View style={{
              flexDirection: 'row',
              alignItems: 'center',
              paddingHorizontal: 16,
              paddingVertical: 12,
              backgroundColor: 'rgba(0,0,0,0.8)',
            }}>
              <TouchableOpacity
                onPress={() => setShowVideoPlayer(false)}
                style={{
                  width: 40,
                  height: 40,
                  alignItems: 'center',
                  justifyContent: 'center',
                  marginRight: 8,
                }}
              >
                <Ionicons name="close" size={24} color="white" />
              </TouchableOpacity>

              <Text style={{
                fontSize: 16,
                fontWeight: '600',
                color: 'white',
                flex: 1,
              }}>
                {selectedVideo.title}
              </Text>
            </View>

            {/* Video Player Area */}
            <View style={{
              flex: 1,
              backgroundColor: '#000',
              alignItems: 'center',
              justifyContent: 'center',
            }}>
              <View style={{
                width: 200,
                height: 200,
                borderRadius: 100,
                backgroundColor: getCategoryColor(selectedVideo.category),
                alignItems: 'center',
                justifyContent: 'center',
                marginBottom: 32,
              }}>
                <Ionicons
                  name={getCategoryIcon(selectedVideo.category) as any}
                  size={80}
                  color="white"
                />
              </View>

              <Text style={{
                fontSize: 20,
                fontWeight: '600',
                color: 'white',
                textAlign: 'center',
                marginBottom: 8,
              }}>
                {selectedVideo.title}
              </Text>

              <Text style={{
                fontSize: 14,
                color: 'rgba(255,255,255,0.7)',
                textAlign: 'center',
                marginBottom: 32,
                paddingHorizontal: 32,
              }}>
                {selectedVideo.description}
              </Text>

              {/* Simulated Video Progress */}
              <View style={{
                width: width - 64,
                marginBottom: 16,
              }}>
                <View style={{
                  height: 4,
                  backgroundColor: 'rgba(255,255,255,0.3)',
                  borderRadius: 2,
                  overflow: 'hidden',
                }}>
                  <View style={{
                    height: '100%',
                    width: `${(currentVideoTime / selectedVideo.duration) * 100}%`,
                    backgroundColor: '#f97316',
                  }} />
                </View>

                <View style={{
                  flexDirection: 'row',
                  justifyContent: 'space-between',
                  marginTop: 8,
                }}>
                  <Text style={{
                    fontSize: 12,
                    color: 'rgba(255,255,255,0.7)',
                  }}>
                    {formatDuration(currentVideoTime)}
                  </Text>
                  <Text style={{
                    fontSize: 12,
                    color: 'rgba(255,255,255,0.7)',
                  }}>
                    {formatDuration(selectedVideo.duration)}
                  </Text>
                </View>
              </View>

              {/* Play Controls */}
              <View style={{
                flexDirection: 'row',
                alignItems: 'center',
                marginBottom: 32,
              }}>
                <TouchableOpacity
                  onPress={() => setCurrentVideoTime(Math.max(0, currentVideoTime - 10))}
                  style={{
                    width: 48,
                    height: 48,
                    borderRadius: 24,
                    backgroundColor: 'rgba(255,255,255,0.2)',
                    alignItems: 'center',
                    justifyContent: 'center',
                    marginHorizontal: 8,
                  }}
                >
                  <Ionicons name="play-back" size={24} color="white" />
                </TouchableOpacity>

                <TouchableOpacity
                  onPress={() => setIsPlaying(!isPlaying)}
                  style={{
                    width: 64,
                    height: 64,
                    borderRadius: 32,
                    backgroundColor: '#f97316',
                    alignItems: 'center',
                    justifyContent: 'center',
                    marginHorizontal: 16,
                  }}
                >
                  <Ionicons
                    name={isPlaying ? 'pause' : 'play'}
                    size={32}
                    color="white"
                  />
                </TouchableOpacity>

                <TouchableOpacity
                  onPress={() => setCurrentVideoTime(Math.min(selectedVideo.duration, currentVideoTime + 10))}
                  style={{
                    width: 48,
                    height: 48,
                    borderRadius: 24,
                    backgroundColor: 'rgba(255,255,255,0.2)',
                    alignItems: 'center',
                    justifyContent: 'center',
                    marginHorizontal: 8,
                  }}
                >
                  <Ionicons name="play-forward" size={24} color="white" />
                </TouchableOpacity>
              </View>

              {/* Complete Video Button */}
              {currentVideoTime >= selectedVideo.duration * 0.9 && (
                <TouchableOpacity
                  onPress={() => completeVideo(selectedVideo.id, 5, 'Great training video!')}
                  style={{
                    backgroundColor: '#10b981',
                    paddingHorizontal: 32,
                    paddingVertical: 12,
                    borderRadius: 24,
                  }}
                >
                  <Text style={{
                    fontSize: 16,
                    fontWeight: '600',
                    color: 'white',
                  }}>
                    Complete Training
                  </Text>
                </TouchableOpacity>
              )}
            </View>
          </SafeAreaView>
        )}
      </Modal>

      {/* Certificates Modal */}
      <Modal
        visible={showCertificates}
        animationType="slide"
        presentationStyle="pageSheet"
      >
        <SafeAreaView style={{ flex: 1, backgroundColor: '#f9fafb' }}>
          {/* Certificates Header */}
          <View style={{
            flexDirection: 'row',
            alignItems: 'center',
            paddingHorizontal: 16,
            paddingVertical: 12,
            backgroundColor: 'white',
            borderBottomWidth: 1,
            borderBottomColor: '#e5e7eb',
          }}>
            <TouchableOpacity
              onPress={() => setShowCertificates(false)}
              style={{
                width: 40,
                height: 40,
                alignItems: 'center',
                justifyContent: 'center',
                marginRight: 8,
              }}
            >
              <Ionicons name="close" size={24} color="#374151" />
            </TouchableOpacity>

            <Text style={{
              fontSize: 18,
              fontWeight: '600',
              color: '#1f2937',
              flex: 1,
            }}>
              My Certificates
            </Text>
          </View>

          {/* Certificates List */}
          <ScrollView style={{ flex: 1, padding: 16 }}>
            {certificates.length === 0 ? (
              <View style={{
                alignItems: 'center',
                justifyContent: 'center',
                paddingVertical: 64,
              }}>
                <Ionicons name="ribbon-outline" size={64} color="#d1d5db" />
                <Text style={{
                  fontSize: 18,
                  fontWeight: '600',
                  color: '#6b7280',
                  marginTop: 16,
                  marginBottom: 8,
                }}>
                  No Certificates Yet
                </Text>
                <Text style={{
                  fontSize: 14,
                  color: '#9ca3af',
                  textAlign: 'center',
                  paddingHorizontal: 32,
                }}>
                  Complete training videos to earn certificates and showcase your skills.
                </Text>
              </View>
            ) : (
              certificates.map((certificate) => (
                <View
                  key={certificate.id}
                  style={{
                    backgroundColor: 'white',
                    borderRadius: 12,
                    padding: 20,
                    marginBottom: 16,
                    borderWidth: 2,
                    borderColor: '#fbbf24',
                  }}
                >
                  <View style={{
                    alignItems: 'center',
                    marginBottom: 16,
                  }}>
                    <View style={{
                      width: 60,
                      height: 60,
                      borderRadius: 30,
                      backgroundColor: '#fbbf24',
                      alignItems: 'center',
                      justifyContent: 'center',
                      marginBottom: 12,
                    }}>
                      <Ionicons name="ribbon" size={30} color="white" />
                    </View>

                    <Text style={{
                      fontSize: 18,
                      fontWeight: 'bold',
                      color: '#1f2937',
                      textAlign: 'center',
                      marginBottom: 4,
                    }}>
                      Certificate of Completion
                    </Text>

                    <Text style={{
                      fontSize: 14,
                      color: '#6b7280',
                      textAlign: 'center',
                    }}>
                      {certificate.certificateNumber}
                    </Text>
                  </View>

                  <View style={{
                    borderTopWidth: 1,
                    borderTopColor: '#f3f4f6',
                    paddingTop: 16,
                  }}>
                    <Text style={{
                      fontSize: 16,
                      fontWeight: '600',
                      color: '#1f2937',
                      textAlign: 'center',
                      marginBottom: 8,
                    }}>
                      {certificate.video.title}
                    </Text>

                    <View style={{
                      flexDirection: 'row',
                      justifyContent: 'space-between',
                      marginBottom: 8,
                    }}>
                      <Text style={{
                        fontSize: 12,
                        color: '#6b7280',
                      }}>
                        Issued: {certificate.issuedAt ? new Date(certificate.issuedAt).toLocaleDateString() : 'N/A'}
                      </Text>
                      <Text style={{
                        fontSize: 12,
                        color: '#6b7280',
                      }}>
                        Score: {certificate.score}%
                      </Text>
                    </View>

                    <Text style={{
                      fontSize: 12,
                      color: '#6b7280',
                      textAlign: 'center',
                    }}>
                      Valid until {certificate.expiresAt ? new Date(certificate.expiresAt).toLocaleDateString() : 'N/A'}
                    </Text>
                  </View>
                </View>
              ))
            )}
          </ScrollView>
        </SafeAreaView>
      </Modal>
    </SafeAreaView>
  );
};

export default TrainingScreen;
