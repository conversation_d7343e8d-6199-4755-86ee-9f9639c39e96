import React, { useState, useEffect, useRef } from 'react';
import {
  View,
  Text,
  ScrollView,
  RefreshControl,
  TouchableOpacity,
  Alert,
  Modal,
  Animated,
  Dimensions,
  StatusBar,
} from 'react-native';
import { SafeAreaView } from 'react-native-safe-area-context';
import { Ionicons } from '@expo/vector-icons';
import { LinearGradient } from 'expo-linear-gradient';
import { BlurView } from 'expo-blur';
import { Card, Switch, Badge, Button, OptimizedImage } from '../../components/ui';
import { useAuth } from '../../context/AuthContext';
import { useOrders } from '../../context/OrderContext';
import { useEarnings } from '../../context/EarningsContext';
import { useNavigation } from '@react-navigation/native';
import { formatCurrency, formatDistance, getInitials } from '../../utils/helpers';
import OrderRequestModal from '../../components/orders/OrderRequestModal';

const { width, height } = Dimensions.get('window');

interface DashboardStats {
  todayEarnings: number;
  todayOrders: number;
  todayDistance: number;
  weeklyEarnings: number;
  rating: number;
  completionRate: number;
  activeOrders: number;
  upcomingBonuses: {
    title: string;
    amount: number;
    progress: number;
    target: number;
  }[];
}

const DashboardScreen: React.FC = () => {
  const navigation = useNavigation();
  const { state: authState } = useAuth();
  const { state: orderState, fetchAvailableOrders } = useOrders();
  const { state: earningsState, fetchEarningsSummary } = useEarnings();
  const [isOnline, setIsOnline] = useState(false);
  const [refreshing, setRefreshing] = useState(false);
  const [showOrderRequest, setShowOrderRequest] = useState(false);

  // Animation refs
  const toggleAnimation = useRef(new Animated.Value(0)).current;
  const fabAnimation = useRef(new Animated.Value(0)).current;
  const statsAnimation = useRef(new Animated.Value(0)).current;
  const pulseAnimation = useRef(new Animated.Value(1)).current;
  const [stats, setStats] = useState<DashboardStats>({
    todayEarnings: 3825,
    todayOrders: 8,
    todayDistance: 45.2,
    weeklyEarnings: 26770,
    rating: 4.8,
    completionRate: 96,
    activeOrders: 2,
    upcomingBonuses: [
      {
        title: 'Complete 10 orders',
        amount: 750,
        progress: 8,
        target: 10,
      },
      {
        title: 'Weekend Warrior',
        amount: 1500,
        progress: 3,
        target: 15,
      },
    ],
  });

  useEffect(() => {
    // Fetch initial data
    fetchAvailableOrders();
    fetchEarningsSummary();

    // Initialize animations
    Animated.stagger(200, [
      Animated.timing(statsAnimation, {
        toValue: 1,
        duration: 800,
        useNativeDriver: true,
      }),
      Animated.timing(fabAnimation, {
        toValue: 1,
        duration: 600,
        useNativeDriver: true,
      }),
    ]).start();

    // Start pulse animation for FAB
    const startPulse = () => {
      Animated.sequence([
        Animated.timing(pulseAnimation, {
          toValue: 1.1,
          duration: 1000,
          useNativeDriver: true,
        }),
        Animated.timing(pulseAnimation, {
          toValue: 1,
          duration: 1000,
          useNativeDriver: true,
        }),
      ]).start(() => startPulse());
    };
    startPulse();

    // Simulate real-time order requests when online
    let orderInterval: NodeJS.Timeout;
    if (isOnline) {
      orderInterval = setInterval(() => {
        // Simulate incoming order request (30% chance every 10 seconds)
        if (Math.random() < 0.3 && orderState.availableOrders.length > 0) {
          setShowOrderRequest(true);
        }
      }, 10000);
    }

    return () => {
      if (orderInterval) {
        clearInterval(orderInterval);
      }
    };
  }, [isOnline]);

  const onRefresh = async () => {
    setRefreshing(true);
    try {
      await Promise.all([
        fetchAvailableOrders(),
        fetchEarningsSummary(),
      ]);

      // Update stats with real data
      setStats(prev => ({
        ...prev,
        activeOrders: orderState.acceptedOrders.length,
        todayEarnings: earningsState.summary?.todayEarnings || prev.todayEarnings,
      }));
    } catch (error) {
      console.error('Error refreshing dashboard:', error);
    } finally {
      setRefreshing(false);
    }
  };

  const toggleOnlineStatus = () => {
    const newStatus = !isOnline;
    setIsOnline(newStatus);

    // Animate toggle
    Animated.timing(toggleAnimation, {
      toValue: newStatus ? 1 : 0,
      duration: 300,
      useNativeDriver: false,
    }).start();

    if (newStatus) {
      Alert.alert(
        '🟢 Going Online',
        'You are now available to receive order requests and start earning! 💰',
        [{ text: 'Let\'s Go! 🚀' }]
      );
      // Start fetching available orders
      fetchAvailableOrders();
    } else {
      Alert.alert(
        '🔴 Going Offline',
        'You will no longer receive new order requests. Take a well-deserved break! 😊',
        [{ text: 'OK' }]
      );
    }
  };

  const renderHeader = () => (
    <LinearGradient
      colors={['#f97316', '#fb923c']}
      style={{
        flexDirection: 'row',
        justifyContent: 'space-between',
        alignItems: 'center',
        paddingHorizontal: 20,
        paddingTop: 16,
        paddingBottom: 20,
      }}
    >
      <View style={{ flexDirection: 'row', alignItems: 'center', flex: 1 }}>
        <TouchableOpacity
          onPress={() => (navigation as any).navigate('Profile')}
          style={{
            width: 60,
            height: 60,
            backgroundColor: 'rgba(255,255,255,0.2)',
            borderRadius: 30,
            justifyContent: 'center',
            alignItems: 'center',
            marginRight: 16,
            borderWidth: 3,
            borderColor: 'rgba(255,255,255,0.3)',
            shadowColor: '#000',
            shadowOffset: { width: 0, height: 4 },
            shadowOpacity: 0.3,
            shadowRadius: 8,
            elevation: 8,
          }}
        >
          <Text style={{ color: 'white', fontWeight: 'bold', fontSize: 22 }}>
            {authState.user ? getInitials(authState.user.firstName, authState.user.lastName) : 'R'}
          </Text>
        </TouchableOpacity>
        <View style={{ flex: 1 }}>
          <Text style={{ fontSize: 24, fontWeight: 'bold', color: 'white', marginBottom: 4 }}>
            Hello, {authState.user?.firstName || 'Rider'}! 👋
          </Text>
          <Text style={{ fontSize: 16, color: 'rgba(255,255,255,0.9)' }}>
            Ready to start delivering?
          </Text>
        </View>
      </View>

      <View style={{ flexDirection: 'row', alignItems: 'center' }}>
        <TouchableOpacity
          style={{
            padding: 12,
            borderRadius: 12,
            backgroundColor: 'rgba(255,255,255,0.2)',
            marginRight: 12,
          }}
          onPress={() => (navigation as any).navigate('Orders')}
        >
          <Ionicons name="notifications-outline" size={24} color="white" />
          {orderState.availableOrders.length > 0 && (
            <View style={{
              position: 'absolute',
              top: 6,
              right: 6,
              backgroundColor: '#dc2626',
              borderRadius: 8,
              minWidth: 16,
              height: 16,
              justifyContent: 'center',
              alignItems: 'center',
            }}>
              <Text style={{ fontSize: 10, color: 'white', fontWeight: 'bold' }}>
                {orderState.availableOrders.length}
              </Text>
            </View>
          )}
        </TouchableOpacity>
      </View>
    </LinearGradient>
  );

  const renderOnlineToggle = () => (
    <View style={{ marginHorizontal: 16, marginTop: -10, marginBottom: 16 }}>
      <Card variant="elevated" style={{
        backgroundColor: 'white',
        borderRadius: 16,
        shadowColor: '#000',
        shadowOffset: { width: 0, height: 4 },
        shadowOpacity: 0.1,
        shadowRadius: 12,
        elevation: 8,
      }}>
        <View style={{ padding: 20 }}>
          <View style={{
            flexDirection: 'row',
            alignItems: 'center',
            justifyContent: 'space-between',
          }}>
            <View style={{ flex: 1 }}>
              <View style={{ flexDirection: 'row', alignItems: 'center', marginBottom: 8 }}>
                <Animated.View style={{
                  width: 16,
                  height: 16,
                  borderRadius: 8,
                  backgroundColor: toggleAnimation.interpolate({
                    inputRange: [0, 1],
                    outputRange: ['#ef4444', '#22c55e'],
                  }),
                  marginRight: 12,
                  transform: [{
                    scale: toggleAnimation.interpolate({
                      inputRange: [0, 1],
                      outputRange: [1, 1.2],
                    }),
                  }],
                }} />
                <Text style={{ fontSize: 20, fontWeight: 'bold', color: '#111827' }}>
                  {isOnline ? '🟢 Online' : '🔴 Offline'}
                </Text>
              </View>
              <Text style={{ fontSize: 16, color: '#6b7280', lineHeight: 22 }}>
                {isOnline
                  ? 'You are receiving order requests and earning money! 💰'
                  : 'Turn on to start receiving orders and earning 🚀'
                }
              </Text>
            </View>

            <Animated.View style={{
              transform: [{
                scale: toggleAnimation.interpolate({
                  inputRange: [0, 1],
                  outputRange: [1, 1.1],
                }),
              }],
            }}>
              <Switch
                value={isOnline}
                onValueChange={toggleOnlineStatus}
                size="lg"
              />
            </Animated.View>
          </View>
        </View>
      </Card>
    </View>
  );

  const renderStatsGrid = () => (
    <Animated.View
      style={{
        opacity: statsAnimation,
        transform: [{
          translateY: statsAnimation.interpolate({
            inputRange: [0, 1],
            outputRange: [50, 0],
          }),
        }],
        marginHorizontal: 16,
        marginBottom: 16,
      }}
    >
      <View style={{
        flexDirection: 'row',
        flexWrap: 'wrap',
        justifyContent: 'space-between',
      }}>
        {/* Earnings Card */}
        <View style={{
          width: '48%',
          backgroundColor: 'white',
          borderRadius: 16,
          padding: 20,
          marginBottom: 12,
          shadowColor: '#000',
          shadowOffset: { width: 0, height: 4 },
          shadowOpacity: 0.1,
          shadowRadius: 12,
          elevation: 8,
        }}>
          <View style={{
            flexDirection: 'row',
            alignItems: 'center',
            marginBottom: 12,
          }}>
            <View style={{
              width: 40,
              height: 40,
              backgroundColor: '#10b981',
              borderRadius: 20,
              justifyContent: 'center',
              alignItems: 'center',
              marginRight: 12,
            }}>
              <Ionicons name="wallet" size={20} color="white" />
            </View>
            <Text style={{ fontSize: 14, color: '#6b7280', fontWeight: '500' }}>
              Earnings
            </Text>
          </View>
          <Text style={{ fontSize: 24, fontWeight: 'bold', color: '#111827', marginBottom: 4 }}>
            {formatCurrency(stats.todayEarnings)}
          </Text>
          <Text style={{ fontSize: 12, color: '#10b981', fontWeight: '500' }}>
            +12% from yesterday
          </Text>
        </View>

        {/* Orders Card */}
        <View style={{
          width: '48%',
          backgroundColor: 'white',
          borderRadius: 16,
          padding: 20,
          marginBottom: 12,
          shadowColor: '#000',
          shadowOffset: { width: 0, height: 4 },
          shadowOpacity: 0.1,
          shadowRadius: 12,
          elevation: 8,
        }}>
          <View style={{
            flexDirection: 'row',
            alignItems: 'center',
            marginBottom: 12,
          }}>
            <View style={{
              width: 40,
              height: 40,
              backgroundColor: '#3b82f6',
              borderRadius: 20,
              justifyContent: 'center',
              alignItems: 'center',
              marginRight: 12,
            }}>
              <Ionicons name="receipt" size={20} color="white" />
            </View>
            <Text style={{ fontSize: 14, color: '#6b7280', fontWeight: '500' }}>
              Orders
            </Text>
          </View>
          <Text style={{ fontSize: 24, fontWeight: 'bold', color: '#111827', marginBottom: 4 }}>
            {stats.todayOrders}
          </Text>
          <Text style={{ fontSize: 12, color: '#3b82f6', fontWeight: '500' }}>
            {stats.activeOrders} active
          </Text>
        </View>

        {/* Rating Card */}
        <View style={{
          width: '48%',
          backgroundColor: 'white',
          borderRadius: 16,
          padding: 20,
          marginBottom: 12,
          shadowColor: '#000',
          shadowOffset: { width: 0, height: 4 },
          shadowOpacity: 0.1,
          shadowRadius: 12,
          elevation: 8,
        }}>
          <View style={{
            flexDirection: 'row',
            alignItems: 'center',
            marginBottom: 12,
          }}>
            <View style={{
              width: 40,
              height: 40,
              backgroundColor: '#f59e0b',
              borderRadius: 20,
              justifyContent: 'center',
              alignItems: 'center',
              marginRight: 12,
            }}>
              <Ionicons name="star" size={20} color="white" />
            </View>
            <Text style={{ fontSize: 14, color: '#6b7280', fontWeight: '500' }}>
              Rating
            </Text>
          </View>
          <View style={{ flexDirection: 'row', alignItems: 'center', marginBottom: 4 }}>
            <Text style={{ fontSize: 24, fontWeight: 'bold', color: '#111827', marginRight: 4 }}>
              {stats.rating}
            </Text>
            <Ionicons name="star" size={16} color="#f59e0b" />
          </View>
          <Text style={{ fontSize: 12, color: '#f59e0b', fontWeight: '500' }}>
            Excellent service!
          </Text>
        </View>

        {/* Bonuses Card */}
        <View style={{
          width: '48%',
          backgroundColor: 'white',
          borderRadius: 16,
          padding: 20,
          marginBottom: 12,
          shadowColor: '#000',
          shadowOffset: { width: 0, height: 4 },
          shadowOpacity: 0.1,
          shadowRadius: 12,
          elevation: 8,
        }}>
          <View style={{
            flexDirection: 'row',
            alignItems: 'center',
            marginBottom: 12,
          }}>
            <View style={{
              width: 40,
              height: 40,
              backgroundColor: '#8b5cf6',
              borderRadius: 20,
              justifyContent: 'center',
              alignItems: 'center',
              marginRight: 12,
            }}>
              <Ionicons name="gift" size={20} color="white" />
            </View>
            <Text style={{ fontSize: 14, color: '#6b7280', fontWeight: '500' }}>
              Bonuses
            </Text>
          </View>
          <Text style={{ fontSize: 24, fontWeight: 'bold', color: '#111827', marginBottom: 4 }}>
            {formatCurrency(stats.upcomingBonuses.reduce((sum, bonus) => sum + bonus.amount, 0))}
          </Text>
          <Text style={{ fontSize: 12, color: '#8b5cf6', fontWeight: '500' }}>
            {stats.upcomingBonuses.length} available
          </Text>
        </View>
      </View>
    </Animated.View>
  );

  const renderQuickActions = () => (
    <Card variant="elevated" margin="md" padding="lg">
      <Text style={{ fontSize: 20, fontWeight: 'bold', color: '#111827', marginBottom: 16 }}>
        Quick Actions
      </Text>

      <View style={{ flexDirection: 'row', justifyContent: 'space-between', marginBottom: 12 }}>
        <TouchableOpacity
          style={{
            flex: 1,
            backgroundColor: '#f97316',
            padding: 16,
            borderRadius: 12,
            alignItems: 'center',
            marginRight: 8,
          }}
          onPress={() => (navigation as any).navigate('OrderRequests')}
        >
          <Ionicons name="notifications" size={24} color="white" style={{ marginBottom: 8 }} />
          <Text style={{ fontSize: 14, fontWeight: '500', color: 'white' }}>
            Order Requests
          </Text>
          {orderState.availableOrders.length > 0 && (
            <View style={{
              position: 'absolute',
              top: 8,
              right: 8,
              backgroundColor: '#dc2626',
              borderRadius: 10,
              minWidth: 20,
              height: 20,
              justifyContent: 'center',
              alignItems: 'center',
            }}>
              <Text style={{ fontSize: 12, color: 'white', fontWeight: 'bold' }}>
                {orderState.availableOrders.length}
              </Text>
            </View>
          )}
        </TouchableOpacity>

        <TouchableOpacity
          style={{
            flex: 1,
            backgroundColor: '#f3f4f6',
            padding: 16,
            borderRadius: 12,
            alignItems: 'center',
            marginHorizontal: 4,
          }}
          onPress={() => (navigation as any).navigate('AdvancedTools')}
        >
          <Ionicons name="construct-outline" size={24} color="#374151" style={{ marginBottom: 8 }} />
          <Text style={{ fontSize: 14, fontWeight: '500', color: '#374151' }}>
            Advanced Tools
          </Text>
        </TouchableOpacity>

        <TouchableOpacity
          style={{
            flex: 1,
            backgroundColor: '#f3f4f6',
            padding: 16,
            borderRadius: 12,
            alignItems: 'center',
            marginLeft: 8,
          }}
          onPress={() => (navigation as any).navigate('Earnings')}
        >
          <Ionicons name="wallet-outline" size={24} color="#374151" style={{ marginBottom: 8 }} />
          <Text style={{ fontSize: 14, fontWeight: '500', color: '#374151' }}>
            Earnings
          </Text>
        </TouchableOpacity>
      </View>

      <View style={{ flexDirection: 'row', justifyContent: 'space-between' }}>
        <TouchableOpacity
          onPress={() => (navigation as any).navigate('Support')}
          style={{
            flex: 1,
            backgroundColor: '#f3f4f6',
            padding: 16,
            borderRadius: 12,
            alignItems: 'center',
            marginRight: 8,
          }}
        >
          <Ionicons name="help-circle-outline" size={24} color="#374151" style={{ marginBottom: 8 }} />
          <Text style={{ fontSize: 14, fontWeight: '500', color: '#374151' }}>
            Support
          </Text>
        </TouchableOpacity>

        <TouchableOpacity
          onPress={() => (navigation as any).navigate('Profile', { screen: 'Settings' })}
          style={{
            flex: 1,
            backgroundColor: '#f3f4f6',
            padding: 16,
            borderRadius: 12,
            alignItems: 'center',
            marginLeft: 8,
          }}
        >
          <Ionicons name="settings-outline" size={24} color="#374151" style={{ marginBottom: 8 }} />
          <Text style={{ fontSize: 14, fontWeight: '500', color: '#374151' }}>
            Settings
          </Text>
        </TouchableOpacity>
      </View>
    </Card>
  );

  const renderActiveOrders = () => (
    <Card variant="elevated" margin="md" padding="lg">
      <View style={{
        flexDirection: 'row',
        justifyContent: 'space-between',
        alignItems: 'center',
        marginBottom: 16,
      }}>
        <Text style={{ fontSize: 20, fontWeight: 'bold', color: '#111827' }}>
          Active Orders
        </Text>
        <Badge
          text={`${stats.activeOrders} active`}
          variant={stats.activeOrders > 0 ? "warning" : "default"}
        />
      </View>

      {stats.activeOrders > 0 ? (
        <View style={{
          backgroundColor: '#fef3c7',
          padding: 16,
          borderRadius: 12,
          borderLeftWidth: 4,
          borderLeftColor: '#f59e0b',
        }}>
          <Text style={{ fontSize: 16, fontWeight: '600', color: '#92400e', marginBottom: 4 }}>
            You have {stats.activeOrders} order{stats.activeOrders > 1 ? 's' : ''} in progress
          </Text>
          <Text style={{ fontSize: 14, color: '#b45309' }}>
            Tap to view details and continue delivery
          </Text>
        </View>
      ) : (
        <View style={{
          backgroundColor: '#f3f4f6',
          padding: 16,
          borderRadius: 12,
          alignItems: 'center',
        }}>
          <Ionicons name="checkmark-circle-outline" size={32} color="#6b7280" />
          <Text style={{ fontSize: 16, color: '#6b7280', marginTop: 8 }}>
            No active orders
          </Text>
          <Text style={{ fontSize: 14, color: '#9ca3af', textAlign: 'center' }}>
            {isOnline ? 'Waiting for new requests...' : 'Go online to receive orders'}
          </Text>
        </View>
      )}
    </Card>
  );

  const renderUpcomingBonuses = () => (
    <Card variant="elevated" margin="md" padding="lg">
      <Text style={{ fontSize: 20, fontWeight: 'bold', color: '#111827', marginBottom: 16 }}>
        Upcoming Bonuses
      </Text>

      {stats.upcomingBonuses.map((bonus, index) => (
        <View key={index} style={{
          backgroundColor: '#f0f9ff',
          padding: 16,
          borderRadius: 12,
          marginBottom: index < stats.upcomingBonuses.length - 1 ? 12 : 0,
          borderLeftWidth: 4,
          borderLeftColor: '#0ea5e9',
        }}>
          <View style={{
            flexDirection: 'row',
            justifyContent: 'space-between',
            alignItems: 'center',
            marginBottom: 8,
          }}>
            <Text style={{ fontSize: 16, fontWeight: '600', color: '#0c4a6e' }}>
              {bonus.title}
            </Text>
            <Text style={{ fontSize: 16, fontWeight: 'bold', color: '#059669' }}>
              +{formatCurrency(bonus.amount)}
            </Text>
          </View>

          <View style={{
            flexDirection: 'row',
            alignItems: 'center',
            marginBottom: 8,
          }}>
            <View style={{
              flex: 1,
              height: 6,
              backgroundColor: '#e0f2fe',
              borderRadius: 3,
              marginRight: 12,
            }}>
              <View style={{
                width: `${(bonus.progress / bonus.target) * 100}%`,
                height: '100%',
                backgroundColor: '#0ea5e9',
                borderRadius: 3,
              }} />
            </View>
            <Text style={{ fontSize: 12, color: '#0c4a6e', fontWeight: '500' }}>
              {bonus.progress}/{bonus.target}
            </Text>
          </View>

          <Text style={{ fontSize: 12, color: '#0369a1' }}>
            {bonus.target - bonus.progress} more to unlock bonus
          </Text>
        </View>
      ))}
    </Card>
  );

  const renderWeeklyOverview = () => (
    <Card variant="elevated" margin="md" padding="lg">
      <Text style={{ fontSize: 20, fontWeight: 'bold', color: '#111827', marginBottom: 16 }}>
        This Week
      </Text>

      <View style={{
        flexDirection: 'row',
        justifyContent: 'space-between',
        alignItems: 'center',
      }}>
        <View>
          <Text style={{ fontSize: 14, color: '#6b7280', marginBottom: 4 }}>
            Total Earnings
          </Text>
          <Text style={{ fontSize: 24, fontWeight: 'bold', color: '#111827' }}>
            {formatCurrency(stats.weeklyEarnings)}
          </Text>
        </View>

        <View style={{ alignItems: 'flex-end' }}>
          <Badge text={`${stats.completionRate}% completion`} variant="success" />
          <Text style={{ fontSize: 12, color: '#6b7280', marginTop: 4 }}>
            Great performance!
          </Text>
        </View>
      </View>
    </Card>
  );

  const renderFAB = () => (
    <Animated.View
      style={{
        position: 'absolute',
        bottom: 100,
        right: 20,
        opacity: fabAnimation,
        transform: [
          {
            scale: Animated.multiply(fabAnimation, pulseAnimation),
          },
        ],
      }}
    >
      <TouchableOpacity
        onPress={() => {
          if (isOnline) {
            (navigation as any).navigate('Orders');
          } else {
            Alert.alert(
              'Go Online First! 🔴',
              'You need to be online to start receiving orders. Turn on your online status first.',
              [
                { text: 'Cancel', style: 'cancel' },
                { text: 'Go Online', onPress: toggleOnlineStatus },
              ]
            );
          }
        }}
        style={{
          width: 70,
          height: 70,
          borderRadius: 35,
          backgroundColor: isOnline ? '#f97316' : '#9ca3af',
          justifyContent: 'center',
          alignItems: 'center',
          shadowColor: '#000',
          shadowOffset: { width: 0, height: 8 },
          shadowOpacity: 0.3,
          shadowRadius: 16,
          elevation: 12,
        }}
      >
        <Ionicons
          name={isOnline ? "rocket" : "power"}
          size={32}
          color="white"
        />
      </TouchableOpacity>

      {/* FAB Label */}
      <View style={{
        position: 'absolute',
        right: 80,
        top: 20,
        backgroundColor: 'rgba(0,0,0,0.8)',
        paddingHorizontal: 12,
        paddingVertical: 8,
        borderRadius: 8,
        opacity: isOnline ? 1 : 0.7,
      }}>
        <Text style={{ color: 'white', fontSize: 14, fontWeight: '600' }}>
          {isOnline ? 'Start Order 🚀' : 'Go Online'}
        </Text>
      </View>
    </Animated.View>
  );

  return (
    <View style={{ flex: 1 }}>
      <StatusBar barStyle="light-content" backgroundColor="#f97316" />

      {/* Map Background */}
      <OptimizedImage
        source={{ uri: 'https://images.unsplash.com/photo-1524661135-423995f22d0b?w=800&h=600&fit=crop' }}
        style={{ flex: 1, position: 'absolute', width: '100%', height: '100%' }}
        width={800}
        height={600}
        lazy={false}
        quality={70}
      />

      <BlurView intensity={20} style={{ flex: 1 }}>
        <SafeAreaView style={{ flex: 1 }}>
          {renderHeader()}

          <ScrollView
            style={{ flex: 1 }}
            refreshControl={
              <RefreshControl
                refreshing={refreshing}
                onRefresh={onRefresh}
                tintColor="#f97316"
                colors={['#f97316']}
              />
            }
            showsVerticalScrollIndicator={false}
          >
            {renderOnlineToggle()}
            {renderStatsGrid()}
            {renderActiveOrders()}
            {renderUpcomingBonuses()}
            {renderQuickActions()}
            {renderWeeklyOverview()}

            {/* Bottom spacing for FAB */}
            <View style={{ height: 100 }} />
          </ScrollView>

          {/* Floating Action Button */}
          {renderFAB()}

          {/* Order Request Modal */}
          <OrderRequestModal
            visible={showOrderRequest}
            onClose={() => setShowOrderRequest(false)}
            orders={orderState.availableOrders}
          />
        </SafeAreaView>
      </BlurView>
    </View>
  );
};

export default DashboardScreen;
