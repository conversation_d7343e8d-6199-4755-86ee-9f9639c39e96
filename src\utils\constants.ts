// API Configuration
export const API_CONFIG = {
  BASE_URL: __DEV__ ? 'http://localhost:3000/api' : 'https://api.foodway.com',
  TIMEOUT: 10000,
  RETRY_ATTEMPTS: 3,
};

// Storage Keys
export const STORAGE_KEYS = {
  AUTH_TOKEN: 'auth_token',
  REFRESH_TOKEN: 'refresh_token',
  USER_DATA: 'user_data',
  RIDER_PROFILE: 'rider_profile',
  APP_SETTINGS: 'app_settings',
  LOCATION_PERMISSION: 'location_permission',
  ONBOARDING_COMPLETED: 'onboarding_completed',
};

// App Configuration
export const APP_CONFIG = {
  NAME: 'FoodWay Rider',
  VERSION: '1.0.0',
  SUPPORT_EMAIL: '<EMAIL>',
  SUPPORT_PHONE: '******-FOODWAY',
  TERMS_URL: 'https://foodway.com/terms',
  PRIVACY_URL: 'https://foodway.com/privacy',
  HELP_URL: 'https://help.foodway.com/rider',
};

// Location Configuration
export const LOCATION_CONFIG = {
  ACCURACY: {
    HIGH: 'high' as const,
    MEDIUM: 'medium' as const,
    LOW: 'low' as const,
  },
  UPDATE_INTERVAL: 5000, // 5 seconds
  DISTANCE_FILTER: 10, // 10 meters
  TIMEOUT: 15000, // 15 seconds
  MAX_AGE: 60000, // 1 minute
};

// Map Configuration
export const MAP_CONFIG = {
  INITIAL_REGION: {
    latitude: 37.7749,
    longitude: -122.4194,
    latitudeDelta: 0.0922,
    longitudeDelta: 0.0421,
  },
  ZOOM_LEVELS: {
    CITY: 0.1,
    NEIGHBORHOOD: 0.01,
    STREET: 0.001,
  },
  MARKER_COLORS: {
    RIDER: '#ef4444',
    PICKUP: '#22c55e',
    DELIVERY: '#dc2626',
    RESTAURANT: '#3b82f6',
  },
};

// Order Status Colors
export const ORDER_STATUS_COLORS = {
  available: '#3b82f6',
  accepted: '#ef4444',
  picked_up: '#8b5cf6',
  delivered: '#22c55e',
  cancelled: '#dc2626',
};

// Payment Method Icons
export const PAYMENT_METHOD_ICONS = {
  cash: 'cash-outline',
  card: 'card-outline',
  digital_wallet: 'wallet-outline',
} as const;

// Vehicle Type Icons
export const VEHICLE_TYPE_ICONS = {
  bicycle: 'bicycle-outline',
  motorcycle: 'bicycle-outline',
  car: 'car-outline',
  scooter: 'bicycle-outline',
} as const;

// Notification Types
export const NOTIFICATION_TYPES = {
  NEW_ORDER: 'new_order',
  ORDER_UPDATE: 'order_update',
  PAYMENT_RECEIVED: 'payment_received',
  SYSTEM_UPDATE: 'system_update',
  PROMOTION: 'promotion',
} as const;

// Error Messages
export const ERROR_MESSAGES = {
  NETWORK_ERROR: 'Network connection error. Please check your internet connection.',
  UNAUTHORIZED: 'Your session has expired. Please log in again.',
  FORBIDDEN: 'You do not have permission to perform this action.',
  NOT_FOUND: 'The requested resource was not found.',
  SERVER_ERROR: 'Server error. Please try again later.',
  VALIDATION_ERROR: 'Please check your input and try again.',
  LOCATION_PERMISSION_DENIED: 'Location permission is required to use this app.',
  LOCATION_UNAVAILABLE: 'Unable to get your current location.',
  CAMERA_PERMISSION_DENIED: 'Camera permission is required to take photos.',
  UNKNOWN_ERROR: 'An unexpected error occurred. Please try again.',
};

// Success Messages
export const SUCCESS_MESSAGES = {
  LOGIN_SUCCESS: 'Welcome back!',
  LOGOUT_SUCCESS: 'You have been logged out successfully.',
  ORDER_ACCEPTED: 'Order accepted successfully!',
  ORDER_PICKED_UP: 'Order marked as picked up.',
  ORDER_DELIVERED: 'Order delivered successfully!',
  PROFILE_UPDATED: 'Profile updated successfully.',
  SETTINGS_SAVED: 'Settings saved successfully.',
  PASSWORD_CHANGED: 'Password changed successfully.',
};

// Validation Rules
export const VALIDATION_RULES = {
  EMAIL_REGEX: /^[^\s@]+@[^\s@]+\.[^\s@]+$/,
  PHONE_REGEX: /^\+?[\d\s\-\(\)]+$/,
  PASSWORD_MIN_LENGTH: 8,
  NAME_MIN_LENGTH: 2,
  NAME_MAX_LENGTH: 50,
  LICENSE_PLATE_REGEX: /^[A-Z0-9\-\s]+$/i,
};

// Time Formats
export const TIME_FORMATS = {
  DATE: 'YYYY-MM-DD',
  TIME: 'HH:mm',
  DATETIME: 'YYYY-MM-DD HH:mm:ss',
  DISPLAY_DATE: 'MMM DD, YYYY',
  DISPLAY_TIME: 'h:mm A',
  DISPLAY_DATETIME: 'MMM DD, YYYY h:mm A',
  SHORT_DATE: 'MMM DD',
  SHORT_DAY: 'ddd',
};

// Currency Configuration
export const CURRENCY_CONFIG = {
  SYMBOL: '₨',
  CODE: 'PKR',
  DECIMAL_PLACES: 0, // Pakistani Rupees typically don't use decimal places
  THOUSANDS_SEPARATOR: ',',
  DECIMAL_SEPARATOR: '.',
};

// Distance Units
export const DISTANCE_UNITS = {
  METRIC: 'metric',
  IMPERIAL: 'imperial',
} as const;

// Theme Colors (matching Tailwind config)
export const THEME_COLORS = {
  primary: {
    50: '#fef2f2',
    100: '#fee2e2',
    200: '#fecaca',
    300: '#fca5a5',
    400: '#f87171',
    500: '#ef4444',
    600: '#dc2626',
    700: '#b91c1c',
    800: '#991b1b',
    900: '#7f1d1d',
  },
  secondary: {
    50: '#f8fafc',
    100: '#f1f5f9',
    200: '#e2e8f0',
    300: '#cbd5e1',
    400: '#94a3b8',
    500: '#64748b',
    600: '#475569',
    700: '#334155',
    800: '#1e293b',
    900: '#0f172a',
  },
  success: {
    50: '#f0fdf4',
    100: '#dcfce7',
    200: '#bbf7d0',
    300: '#86efac',
    400: '#4ade80',
    500: '#22c55e',
    600: '#16a34a',
    700: '#15803d',
    800: '#166534',
    900: '#14532d',
  },
  warning: {
    50: '#fffbeb',
    100: '#fef3c7',
    200: '#fde68a',
    300: '#fcd34d',
    400: '#fbbf24',
    500: '#f59e0b',
    600: '#d97706',
    700: '#b45309',
    800: '#92400e',
    900: '#78350f',
  },
  error: {
    50: '#fef2f2',
    100: '#fee2e2',
    200: '#fecaca',
    300: '#fca5a5',
    400: '#f87171',
    500: '#ef4444',
    600: '#dc2626',
    700: '#b91c1c',
    800: '#991b1b',
    900: '#7f1d1d',
  },
};
