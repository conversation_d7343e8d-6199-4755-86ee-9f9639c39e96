import React, { useState, useEffect } from 'react';
import {
  View,
  Text,
  ScrollView,
  TouchableOpacity,
  Image,
  RefreshControl,
  Alert,
} from 'react-native';
import { SafeAreaView } from 'react-native-safe-area-context';
import { Ionicons } from '@expo/vector-icons';
import { useNavigation } from '@react-navigation/native';
import * as ImagePicker from 'expo-image-picker';
import { Card, Button, Badge, LoadingSpinner } from '../../components/ui';
import { useAuth } from '../../context/AuthContext';
import { formatDate } from '../../utils/helpers';
import { DocumentStatus, VehicleType } from '../../types/profile';

// Mock profile data
const mockProfile = {
  id: '1',
  userId: 'user1',
  personalInfo: {
    firstName: 'Muhammad',
    lastName: 'Ahmed',
    email: '<EMAIL>',
    phone: '+92 300 1234567',
    cnic: '35202-1234567-1',
    dateOfBirth: '1995-06-15',
    address: 'House 123, Street 5, DHA Phase 2',
    city: 'Lahore',
    profilePicture: 'https://images.unsplash.com/photo-1507003211169-0a1dd7228f2d?w=150&h=150&fit=crop&crop=face',
  },
  riderInfo: {
    riderCode: 'FW-LHR-001234',
    joinedDate: '2023-01-15',
    status: 'active' as const,
    rating: 4.7,
    totalDeliveries: 1247,
    completionRate: 94.5,
    onTimeRate: 89.2,
    preferredZones: [
      { zoneId: '1', zoneName: 'DHA & Gulberg', city: 'Lahore', priority: 1, isActive: true },
      { zoneId: '2', zoneName: 'Model Town', city: 'Lahore', priority: 2, isActive: true },
      { zoneId: '3', zoneName: 'Johar Town', city: 'Lahore', priority: 3, isActive: false },
    ],
    workingHours: {
      start: '09:00',
      end: '22:00',
      workingDays: [1, 2, 3, 4, 5, 6, 0], // Monday to Sunday
    },
  },
  documents: [
    { id: '1', type: 'cnic' as const, name: 'CNIC', status: DocumentStatus.VERIFIED, uploadedAt: '2023-01-16', verifiedAt: '2023-01-17' },
    { id: '2', type: 'license' as const, name: 'Driving License', status: DocumentStatus.VERIFIED, uploadedAt: '2023-01-16', verifiedAt: '2023-01-18', expiryDate: '2028-06-15' },
    { id: '3', type: 'vehicle_registration' as const, name: 'Vehicle Registration', status: DocumentStatus.PENDING, uploadedAt: '2023-01-20' },
    { id: '4', type: 'insurance' as const, name: 'Vehicle Insurance', status: DocumentStatus.NOT_UPLOADED },
  ],
  vehicles: [
    {
      id: '1',
      type: VehicleType.MOTORCYCLE,
      make: 'Honda',
      model: 'CD 70',
      year: 2022,
      color: 'Red',
      plateNumber: 'LES-1234',
      isActive: true,
      createdAt: '2023-01-16',
      updatedAt: '2023-01-16',
      documents: [],
    },
  ],
  paymentMethods: [
    {
      id: '1',
      type: 'bank_account' as const,
      name: 'HBL Account',
      isDefault: true,
      isActive: true,
      createdAt: '2023-01-16',
      updatedAt: '2023-01-16',
      details: {
        bankName: 'Habib Bank Limited',
        accountTitle: 'Muhammad Ahmed',
        accountNumber: '***********',
        iban: '************************',
      },
    },
  ],
  emergencyContact: {
    name: 'Fatima Ahmed',
    relationship: 'Sister',
    phone: '+92 301 2345678',
  },
  createdAt: '2023-01-15',
  updatedAt: '2024-01-20',
};

const ProfileScreen: React.FC = () => {
  const navigation = useNavigation();
  const { state, logout } = useAuth();
  const [profile, setProfile] = useState(mockProfile);
  const [refreshing, setRefreshing] = useState(false);
  const [uploading, setUploading] = useState(false);

  const onRefresh = async () => {
    setRefreshing(true);
    // Simulate API call
    await new Promise(resolve => setTimeout(resolve, 1000));
    setRefreshing(false);
  };

  const handleLogout = () => {
    Alert.alert(
      'Logout',
      'Are you sure you want to logout?',
      [
        { text: 'Cancel', style: 'cancel' },
        {
          text: 'Logout',
          style: 'destructive',
          onPress: async () => {
            try {
              await logout();
            } catch (error: any) {
              Alert.alert('Error', error.message);
            }
          },
        },
      ]
    );
  };

  const handleProfilePicturePress = async () => {
    const { status } = await ImagePicker.requestMediaLibraryPermissionsAsync();
    if (status !== 'granted') {
      Alert.alert('Permission Required', 'Please grant camera roll permissions to upload profile picture.');
      return;
    }

    Alert.alert(
      'Update Profile Picture',
      'Choose an option',
      [
        { text: 'Camera', onPress: () => openCamera() },
        { text: 'Gallery', onPress: () => openGallery() },
        { text: 'Cancel', style: 'cancel' },
      ]
    );
  };

  const openCamera = async () => {
    const { status } = await ImagePicker.requestCameraPermissionsAsync();
    if (status !== 'granted') {
      Alert.alert('Permission Required', 'Please grant camera permissions to take a photo.');
      return;
    }

    const result = await ImagePicker.launchCameraAsync({
      mediaTypes: ImagePicker.MediaTypeOptions.Images,
      allowsEditing: true,
      aspect: [1, 1],
      quality: 0.8,
    });

    if (!result.canceled) {
      uploadProfilePicture(result.assets[0].uri);
    }
  };

  const openGallery = async () => {
    const result = await ImagePicker.launchImageLibraryAsync({
      mediaTypes: ImagePicker.MediaTypeOptions.Images,
      allowsEditing: true,
      aspect: [1, 1],
      quality: 0.8,
    });

    if (!result.canceled) {
      uploadProfilePicture(result.assets[0].uri);
    }
  };

  const uploadProfilePicture = async (uri: string) => {
    setUploading(true);
    try {
      // Simulate upload
      await new Promise(resolve => setTimeout(resolve, 2000));
      setProfile(prev => ({
        ...prev,
        personalInfo: {
          ...prev.personalInfo,
          profilePicture: uri,
        },
      }));
      Alert.alert('Success', 'Profile picture updated successfully!');
    } catch (error) {
      Alert.alert('Error', 'Failed to upload profile picture. Please try again.');
    } finally {
      setUploading(false);
    }
  };

  const getDocumentStatusColor = (status: DocumentStatus) => {
    switch (status) {
      case DocumentStatus.VERIFIED:
        return '#10b981';
      case DocumentStatus.PENDING:
        return '#f59e0b';
      case DocumentStatus.REJECTED:
        return '#ef4444';
      case DocumentStatus.EXPIRED:
        return '#ef4444';
      case DocumentStatus.NOT_UPLOADED:
        return '#6b7280';
      default:
        return '#6b7280';
    }
  };

  const getDocumentStatusText = (status: DocumentStatus) => {
    switch (status) {
      case DocumentStatus.VERIFIED:
        return 'Verified';
      case DocumentStatus.PENDING:
        return 'Pending';
      case DocumentStatus.REJECTED:
        return 'Rejected';
      case DocumentStatus.EXPIRED:
        return 'Expired';
      case DocumentStatus.NOT_UPLOADED:
        return 'Not Uploaded';
      default:
        return 'Unknown';
    }
  };

  const renderProfileHeader = () => (
    <Card variant="elevated" margin="md" padding="lg">
      <View style={{ alignItems: 'center', marginBottom: 20 }}>
        <TouchableOpacity onPress={handleProfilePicturePress} disabled={uploading}>
          <View style={{ position: 'relative' }}>
            <Image
              source={{
                uri: profile.personalInfo.profilePicture || 'https://via.placeholder.com/120x120/e5e7eb/6b7280?text=No+Image'
              }}
              style={{
                width: 120,
                height: 120,
                borderRadius: 60,
                backgroundColor: '#f3f4f6',
              }}
            />
            {uploading && (
              <View style={{
                position: 'absolute',
                top: 0,
                left: 0,
                right: 0,
                bottom: 0,
                backgroundColor: 'rgba(0,0,0,0.5)',
                borderRadius: 60,
                alignItems: 'center',
                justifyContent: 'center',
              }}>
                <LoadingSpinner size="small" color="white" />
              </View>
            )}
            <View style={{
              position: 'absolute',
              bottom: 0,
              right: 0,
              backgroundColor: '#f97316',
              borderRadius: 16,
              width: 32,
              height: 32,
              alignItems: 'center',
              justifyContent: 'center',
              borderWidth: 2,
              borderColor: 'white',
            }}>
              <Ionicons name="camera" size={16} color="white" />
            </View>
          </View>
        </TouchableOpacity>

        <Text style={{ fontSize: 24, fontWeight: 'bold', color: '#111827', marginTop: 16 }}>
          {profile.personalInfo.firstName} {profile.personalInfo.lastName}
        </Text>
        <Text style={{ fontSize: 16, color: '#6b7280', marginTop: 4 }}>
          {profile.riderInfo.riderCode}
        </Text>

        <View style={{ flexDirection: 'row', alignItems: 'center', marginTop: 8 }}>
          <Badge
            text={profile.riderInfo.status.charAt(0).toUpperCase() + profile.riderInfo.status.slice(1)}
            style={{
              backgroundColor: profile.riderInfo.status === 'active' ? '#10b981' : '#6b7280',
              marginRight: 8,
            }}
          />
          <View style={{ flexDirection: 'row', alignItems: 'center' }}>
            <Ionicons name="star" size={16} color="#f59e0b" />
            <Text style={{ fontSize: 14, fontWeight: '600', color: '#111827', marginLeft: 4 }}>
              {profile.riderInfo.rating.toFixed(1)}
            </Text>
          </View>
        </View>
      </View>

      <View style={{ flexDirection: 'row', justifyContent: 'space-between' }}>
        <View style={{ alignItems: 'center', flex: 1 }}>
          <Text style={{ fontSize: 20, fontWeight: 'bold', color: '#111827' }}>
            {profile.riderInfo.totalDeliveries}
          </Text>
          <Text style={{ fontSize: 12, color: '#6b7280' }}>Deliveries</Text>
        </View>
        <View style={{ alignItems: 'center', flex: 1 }}>
          <Text style={{ fontSize: 20, fontWeight: 'bold', color: '#10b981' }}>
            {profile.riderInfo.completionRate.toFixed(1)}%
          </Text>
          <Text style={{ fontSize: 12, color: '#6b7280' }}>Completion</Text>
        </View>
        <View style={{ alignItems: 'center', flex: 1 }}>
          <Text style={{ fontSize: 20, fontWeight: 'bold', color: '#3b82f6' }}>
            {profile.riderInfo.onTimeRate.toFixed(1)}%
          </Text>
          <Text style={{ fontSize: 12, color: '#6b7280' }}>On Time</Text>
        </View>
      </View>
    </Card>
  );

  const renderPersonalInfo = () => (
    <Card variant="elevated" margin="md" padding="lg">
      <View style={{ flexDirection: 'row', justifyContent: 'space-between', alignItems: 'center', marginBottom: 16 }}>
        <Text style={{ fontSize: 18, fontWeight: 'bold', color: '#111827' }}>
          Personal Information
        </Text>
        <TouchableOpacity
          onPress={() => navigation.navigate('EditProfile' as never)}
          style={{
            flexDirection: 'row',
            alignItems: 'center',
            backgroundColor: '#f97316',
            paddingHorizontal: 12,
            paddingVertical: 6,
            borderRadius: 6,
          }}
        >
          <Ionicons name="pencil" size={14} color="white" />
          <Text style={{ fontSize: 12, fontWeight: '600', color: 'white', marginLeft: 4 }}>
            Edit
          </Text>
        </TouchableOpacity>
      </View>

      <View style={{ gap: 12 }}>
        <View style={{ flexDirection: 'row', justifyContent: 'space-between' }}>
          <Text style={{ fontSize: 14, color: '#6b7280', flex: 1 }}>Email</Text>
          <Text style={{ fontSize: 14, color: '#111827', flex: 2, textAlign: 'right' }}>
            {profile.personalInfo.email}
          </Text>
        </View>

        <View style={{ flexDirection: 'row', justifyContent: 'space-between' }}>
          <Text style={{ fontSize: 14, color: '#6b7280', flex: 1 }}>Phone</Text>
          <Text style={{ fontSize: 14, color: '#111827', flex: 2, textAlign: 'right' }}>
            {profile.personalInfo.phone}
          </Text>
        </View>

        <View style={{ flexDirection: 'row', justifyContent: 'space-between' }}>
          <Text style={{ fontSize: 14, color: '#6b7280', flex: 1 }}>CNIC</Text>
          <Text style={{ fontSize: 14, color: '#111827', flex: 2, textAlign: 'right' }}>
            {profile.personalInfo.cnic}
          </Text>
        </View>

        <View style={{ flexDirection: 'row', justifyContent: 'space-between' }}>
          <Text style={{ fontSize: 14, color: '#6b7280', flex: 1 }}>City</Text>
          <Text style={{ fontSize: 14, color: '#111827', flex: 2, textAlign: 'right' }}>
            {profile.personalInfo.city}
          </Text>
        </View>

        <View style={{ flexDirection: 'row', justifyContent: 'space-between' }}>
          <Text style={{ fontSize: 14, color: '#6b7280', flex: 1 }}>Joined</Text>
          <Text style={{ fontSize: 14, color: '#111827', flex: 2, textAlign: 'right' }}>
            {formatDate(profile.riderInfo.joinedDate)}
          </Text>
        </View>
      </View>
    </Card>
  );

  const renderDocumentStatus = () => (
    <Card variant="elevated" margin="md" padding="lg">
      <View style={{ flexDirection: 'row', justifyContent: 'space-between', alignItems: 'center', marginBottom: 16 }}>
        <Text style={{ fontSize: 18, fontWeight: 'bold', color: '#111827' }}>
          Document Status
        </Text>
        <TouchableOpacity
          onPress={() => navigation.navigate('DocumentStatus' as never)}
          style={{
            flexDirection: 'row',
            alignItems: 'center',
            backgroundColor: '#3b82f6',
            paddingHorizontal: 12,
            paddingVertical: 6,
            borderRadius: 6,
          }}
        >
          <Ionicons name="document-text" size={14} color="white" />
          <Text style={{ fontSize: 12, fontWeight: '600', color: 'white', marginLeft: 4 }}>
            Manage
          </Text>
        </TouchableOpacity>
      </View>

      <View style={{ gap: 12 }}>
        {profile.documents.map((doc) => (
          <View key={doc.id} style={{ flexDirection: 'row', justifyContent: 'space-between', alignItems: 'center' }}>
            <Text style={{ fontSize: 14, color: '#111827', flex: 1 }}>
              {doc.name}
            </Text>
            <Badge
              text={getDocumentStatusText(doc.status)}
              style={{ backgroundColor: getDocumentStatusColor(doc.status) }}
            />
          </View>
        ))}
      </View>

      {profile.documents.some(doc => doc.status === DocumentStatus.NOT_UPLOADED || doc.status === DocumentStatus.REJECTED) && (
        <View style={{ marginTop: 12, padding: 12, backgroundColor: '#fef3e2', borderRadius: 8, borderLeftWidth: 4, borderLeftColor: '#f97316' }}>
          <Text style={{ fontSize: 12, color: '#92400e', fontWeight: '600' }}>
            ⚠️ Action Required
          </Text>
          <Text style={{ fontSize: 12, color: '#92400e', marginTop: 2 }}>
            Please upload missing documents to continue receiving orders.
          </Text>
        </View>
      )}
    </Card>
  );

  const renderQuickActions = () => (
    <Card variant="elevated" margin="md" padding="lg">
      <Text style={{ fontSize: 18, fontWeight: 'bold', color: '#111827', marginBottom: 16 }}>
        Quick Actions
      </Text>

      <View style={{ flexDirection: 'row', justifyContent: 'space-between', gap: 12 }}>
        <TouchableOpacity
          onPress={() => navigation.navigate('VehicleInfo' as never)}
          style={{
            flex: 1,
            backgroundColor: '#f3f4f6',
            padding: 16,
            borderRadius: 12,
            alignItems: 'center',
          }}
        >
          <Ionicons name="car" size={24} color="#f97316" />
          <Text style={{ fontSize: 12, fontWeight: '600', color: '#111827', marginTop: 8, textAlign: 'center' }}>
            Vehicle Info
          </Text>
        </TouchableOpacity>

        <TouchableOpacity
          onPress={() => navigation.navigate('PaymentMethods' as never)}
          style={{
            flex: 1,
            backgroundColor: '#f3f4f6',
            padding: 16,
            borderRadius: 12,
            alignItems: 'center',
          }}
        >
          <Ionicons name="card" size={24} color="#10b981" />
          <Text style={{ fontSize: 12, fontWeight: '600', color: '#111827', marginTop: 8, textAlign: 'center' }}>
            Payment
          </Text>
        </TouchableOpacity>

        <TouchableOpacity
          onPress={() => navigation.navigate('Settings' as never)}
          style={{
            flex: 1,
            backgroundColor: '#f3f4f6',
            padding: 16,
            borderRadius: 12,
            alignItems: 'center',
          }}
        >
          <Ionicons name="settings" size={24} color="#3b82f6" />
          <Text style={{ fontSize: 12, fontWeight: '600', color: '#111827', marginTop: 8, textAlign: 'center' }}>
            Settings
          </Text>
        </TouchableOpacity>
      </View>
    </Card>
  );

  return (
    <SafeAreaView style={{ flex: 1, backgroundColor: '#f8fafc' }}>
      <View style={{
        paddingHorizontal: 20,
        paddingVertical: 16,
        backgroundColor: '#ffffff',
        borderBottomWidth: 1,
        borderBottomColor: '#e5e7eb',
        flexDirection: 'row',
        alignItems: 'center',
        justifyContent: 'space-between',
      }}>
        <Text style={{ fontSize: 24, fontWeight: 'bold', color: '#111827' }}>
          Profile
        </Text>
        <TouchableOpacity
          onPress={() => navigation.navigate('Settings' as never)}
          style={{
            padding: 8,
            borderRadius: 8,
            backgroundColor: '#f3f4f6',
          }}
        >
          <Ionicons name="settings-outline" size={20} color="#6b7280" />
        </TouchableOpacity>
      </View>

      <ScrollView
        style={{ flex: 1 }}
        refreshControl={
          <RefreshControl refreshing={refreshing} onRefresh={onRefresh} />
        }
        showsVerticalScrollIndicator={false}
      >
        {renderProfileHeader()}
        {renderPersonalInfo()}
        {renderDocumentStatus()}
        {renderQuickActions()}

        <View style={{ padding: 20 }}>
          <Button
            title="Logout"
            variant="outline"
            leftIcon="log-out-outline"
            onPress={handleLogout}
            style={{ borderColor: '#ef4444' }}
            textStyle={{ color: '#ef4444' }}
          />
        </View>

        {/* Bottom spacing */}
        <View style={{ height: 20 }} />
      </ScrollView>
    </SafeAreaView>
  );
};

export default ProfileScreen;
