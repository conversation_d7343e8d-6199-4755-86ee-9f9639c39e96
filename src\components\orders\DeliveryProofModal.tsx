import React, { useState } from 'react';
import {
  View,
  Text,
  Modal,
  TouchableOpacity,
  Alert,
  Image,
  TextInput,
  ScrollView,
  Dimensions,
} from 'react-native';
import { Ionicons } from '@expo/vector-icons';
import * as ImagePicker from 'expo-image-picker';
import { <PERSON><PERSON>, <PERSON> } from '../ui';

const { width, height } = Dimensions.get('window');

interface DeliveryProofModalProps {
  visible: boolean;
  onClose: () => void;
  onConfirm: (proofData: DeliveryProofData) => void;
  orderNumber: string;
}

interface DeliveryProofData {
  photo?: string;
  notes?: string;
  timestamp: Date;
  location?: {
    latitude: number;
    longitude: number;
  };
}

const DeliveryProofModal: React.FC<DeliveryProofModalProps> = ({
  visible,
  onClose,
  onConfirm,
  orderNumber,
}) => {
  const [photo, setPhoto] = useState<string | null>(null);
  const [notes, setNotes] = useState('');
  const [isLoading, setIsLoading] = useState(false);

  const handleTakePhoto = async () => {
    try {
      const { status } = await ImagePicker.requestCameraPermissionsAsync();
      if (status !== 'granted') {
        Alert.alert('Permission Required', 'Camera permission is needed to take delivery photos');
        return;
      }

      const result = await ImagePicker.launchCameraAsync({
        mediaTypes: ImagePicker.MediaTypeOptions.Images,
        allowsEditing: true,
        aspect: [4, 3],
        quality: 0.8,
      });

      if (!result.canceled && result.assets[0]) {
        setPhoto(result.assets[0].uri);
      }
    } catch (error) {
      console.error('Error taking photo:', error);
      Alert.alert('Error', 'Could not take photo');
    }
  };

  const handleSelectFromGallery = async () => {
    try {
      const { status } = await ImagePicker.requestMediaLibraryPermissionsAsync();
      if (status !== 'granted') {
        Alert.alert('Permission Required', 'Gallery permission is needed to select photos');
        return;
      }

      const result = await ImagePicker.launchImageLibraryAsync({
        mediaTypes: ImagePicker.MediaTypeOptions.Images,
        allowsEditing: true,
        aspect: [4, 3],
        quality: 0.8,
      });

      if (!result.canceled && result.assets[0]) {
        setPhoto(result.assets[0].uri);
      }
    } catch (error) {
      console.error('Error selecting photo:', error);
      Alert.alert('Error', 'Could not select photo');
    }
  };

  const handleConfirmDelivery = async () => {
    if (!photo) {
      Alert.alert('Photo Required', 'Please take a photo as proof of delivery');
      return;
    }

    setIsLoading(true);

    try {
      // In a real app, you would upload the photo to your server here
      const proofData: DeliveryProofData = {
        photo,
        notes: notes.trim(),
        timestamp: new Date(),
        // In a real app, you would get the actual location here
        location: {
          latitude: 0,
          longitude: 0,
        },
      };

      onConfirm(proofData);
    } catch (error) {
      console.error('Error confirming delivery:', error);
      Alert.alert('Error', 'Could not confirm delivery');
    } finally {
      setIsLoading(false);
    }
  };

  const handleClose = () => {
    setPhoto(null);
    setNotes('');
    onClose();
  };

  return (
    <Modal
      visible={visible}
      animationType="slide"
      presentationStyle="pageSheet"
      onRequestClose={handleClose}
    >
      <View style={{
        flex: 1,
        backgroundColor: '#f8fafc',
      }}>
        {/* Header */}
        <View style={{
          flexDirection: 'row',
          alignItems: 'center',
          justifyContent: 'space-between',
          paddingHorizontal: 20,
          paddingVertical: 16,
          backgroundColor: '#ffffff',
          borderBottomWidth: 1,
          borderBottomColor: '#e5e7eb',
        }}>
          <TouchableOpacity
            onPress={handleClose}
            style={{
              width: 40,
              height: 40,
              borderRadius: 20,
              backgroundColor: '#f3f4f6',
              justifyContent: 'center',
              alignItems: 'center',
            }}
          >
            <Ionicons name="close" size={20} color="#374151" />
          </TouchableOpacity>
          
          <Text style={{
            fontSize: 18,
            fontWeight: 'bold',
            color: '#111827',
          }}>
            Delivery Proof
          </Text>
          
          <View style={{ width: 40 }} />
        </View>

        <ScrollView style={{ flex: 1 }} showsVerticalScrollIndicator={false}>
          {/* Order Info */}
          <Card variant="elevated" margin="md" padding="lg">
            <View style={{
              flexDirection: 'row',
              alignItems: 'center',
              marginBottom: 8,
            }}>
              <Ionicons name="receipt" size={20} color="#f97316" />
              <Text style={{
                fontSize: 16,
                fontWeight: 'bold',
                color: '#111827',
                marginLeft: 8,
              }}>
                Order #{orderNumber}
              </Text>
            </View>
            <Text style={{
              fontSize: 14,
              color: '#6b7280',
            }}>
              Please take a photo as proof of delivery and add any additional notes.
            </Text>
          </Card>

          {/* Photo Section */}
          <Card variant="elevated" margin="md" padding="lg">
            <Text style={{
              fontSize: 16,
              fontWeight: 'bold',
              color: '#111827',
              marginBottom: 16,
            }}>
              Delivery Photo *
            </Text>

            {photo ? (
              <View style={{
                alignItems: 'center',
                marginBottom: 16,
              }}>
                <Image
                  source={{ uri: photo }}
                  style={{
                    width: width - 80,
                    height: (width - 80) * 0.75,
                    borderRadius: 12,
                    marginBottom: 12,
                  }}
                  resizeMode="cover"
                />
                <TouchableOpacity
                  onPress={() => setPhoto(null)}
                  style={{
                    flexDirection: 'row',
                    alignItems: 'center',
                    backgroundColor: '#fee2e2',
                    paddingHorizontal: 12,
                    paddingVertical: 6,
                    borderRadius: 20,
                  }}
                >
                  <Ionicons name="trash" size={16} color="#dc2626" />
                  <Text style={{
                    fontSize: 14,
                    color: '#dc2626',
                    marginLeft: 4,
                  }}>
                    Remove Photo
                  </Text>
                </TouchableOpacity>
              </View>
            ) : (
              <View style={{
                backgroundColor: '#f8fafc',
                borderWidth: 2,
                borderColor: '#e5e7eb',
                borderStyle: 'dashed',
                borderRadius: 12,
                padding: 32,
                alignItems: 'center',
                marginBottom: 16,
              }}>
                <Ionicons name="camera" size={48} color="#9ca3af" />
                <Text style={{
                  fontSize: 16,
                  color: '#6b7280',
                  marginTop: 8,
                  textAlign: 'center',
                }}>
                  Take a photo of the delivered order
                </Text>
              </View>
            )}

            <View style={{
              flexDirection: 'row',
              justifyContent: 'space-around',
            }}>
              <TouchableOpacity
                onPress={handleTakePhoto}
                style={{
                  flexDirection: 'row',
                  alignItems: 'center',
                  backgroundColor: '#3b82f6',
                  paddingHorizontal: 20,
                  paddingVertical: 12,
                  borderRadius: 25,
                  flex: 1,
                  marginRight: 8,
                  justifyContent: 'center',
                }}
              >
                <Ionicons name="camera" size={20} color="white" />
                <Text style={{
                  fontSize: 14,
                  fontWeight: '600',
                  color: 'white',
                  marginLeft: 8,
                }}>
                  Take Photo
                </Text>
              </TouchableOpacity>

              <TouchableOpacity
                onPress={handleSelectFromGallery}
                style={{
                  flexDirection: 'row',
                  alignItems: 'center',
                  backgroundColor: '#6b7280',
                  paddingHorizontal: 20,
                  paddingVertical: 12,
                  borderRadius: 25,
                  flex: 1,
                  marginLeft: 8,
                  justifyContent: 'center',
                }}
              >
                <Ionicons name="images" size={20} color="white" />
                <Text style={{
                  fontSize: 14,
                  fontWeight: '600',
                  color: 'white',
                  marginLeft: 8,
                }}>
                  Gallery
                </Text>
              </TouchableOpacity>
            </View>
          </Card>

          {/* Notes Section */}
          <Card variant="elevated" margin="md" padding="lg">
            <Text style={{
              fontSize: 16,
              fontWeight: 'bold',
              color: '#111827',
              marginBottom: 12,
            }}>
              Additional Notes (Optional)
            </Text>

            <TextInput
              style={{
                backgroundColor: '#f8fafc',
                borderWidth: 1,
                borderColor: '#e5e7eb',
                borderRadius: 12,
                padding: 16,
                fontSize: 16,
                color: '#111827',
                textAlignVertical: 'top',
                minHeight: 100,
              }}
              placeholder="Add any notes about the delivery..."
              placeholderTextColor="#9ca3af"
              value={notes}
              onChangeText={setNotes}
              multiline
              numberOfLines={4}
            />
          </Card>

          {/* Bottom spacing */}
          <View style={{ height: 100 }} />
        </ScrollView>

        {/* Bottom Action */}
        <View style={{
          backgroundColor: '#ffffff',
          paddingHorizontal: 20,
          paddingVertical: 16,
          borderTopWidth: 1,
          borderTopColor: '#e5e7eb',
        }}>
          <Button
            title="Confirm Delivery"
            onPress={handleConfirmDelivery}
            disabled={!photo || isLoading}
            loading={isLoading}
            fullWidth
            size="lg"
            leftIcon="checkmark-circle"
          />
        </View>
      </View>
    </Modal>
  );
};

export default DeliveryProofModal;
