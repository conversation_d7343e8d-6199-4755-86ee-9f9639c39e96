// Profile & Settings Types

export enum DocumentStatus {
  PENDING = 'pending',
  VERIFIED = 'verified',
  REJECTED = 'rejected',
  EXPIRED = 'expired',
  NOT_UPLOADED = 'not_uploaded',
}

export enum VehicleType {
  MOTORCYCLE = 'motorcycle',
  CAR = 'car',
  BICYCLE = 'bicycle',
  SCOOTER = 'scooter',
}

export enum PaymentMethodType {
  BANK_ACCOUNT = 'bank_account',
  JAZZCASH = 'jazzcash',
  EASYPAISA = 'easypaisa',
  SADAPAY = 'sadapay',
  NAYAPAY = 'nayapay',
}

export enum NotificationType {
  ORDER_REQUESTS = 'order_requests',
  EARNINGS_UPDATES = 'earnings_updates',
  PROMOTIONS = 'promotions',
  SYSTEM_UPDATES = 'system_updates',
  RATING_FEEDBACK = 'rating_feedback',
  PAYMENT_UPDATES = 'payment_updates',
}

export enum Language {
  ENGLISH = 'en',
  URDU = 'ur',
  PUNJABI = 'pa',
}

export enum PrivacySetting {
  LOCATION_SHARING = 'location_sharing',
  CONTACT_VISIBILITY = 'contact_visibility',
  RATING_VISIBILITY = 'rating_visibility',
  EARNINGS_VISIBILITY = 'earnings_visibility',
}

// Document interfaces
export interface Document {
  id: string;
  type: 'cnic' | 'license' | 'vehicle_registration' | 'insurance' | 'passport';
  name: string;
  url?: string;
  status: DocumentStatus;
  uploadedAt?: string;
  verifiedAt?: string;
  expiryDate?: string;
  rejectionReason?: string;
  notes?: string;
}

// Vehicle interfaces
export interface Vehicle {
  id: string;
  type: VehicleType;
  make: string;
  model: string;
  year: number;
  color: string;
  plateNumber: string;
  engineNumber?: string;
  chassisNumber?: string;
  registrationDate?: string;
  insuranceExpiry?: string;
  fitnessExpiry?: string;
  documents: Document[];
  isActive: boolean;
  createdAt: string;
  updatedAt: string;
}

// Payment method interfaces
export interface BankAccount {
  id: string;
  bankName: string;
  accountTitle: string;
  accountNumber: string;
  iban: string;
  branchCode?: string;
  branchName?: string;
  isDefault: boolean;
  isVerified: boolean;
  createdAt: string;
}

export interface MobileWallet {
  id: string;
  type: PaymentMethodType;
  phoneNumber: string;
  accountName: string;
  isDefault: boolean;
  isVerified: boolean;
  createdAt: string;
}

export interface PaymentMethod {
  id: string;
  type: PaymentMethodType;
  name: string;
  details: BankAccount | MobileWallet;
  isDefault: boolean;
  isActive: boolean;
  createdAt: string;
  updatedAt: string;
}

// Zone and location interfaces
export interface Zone {
  id: string;
  name: string;
  city: string;
  areas: string[];
  isActive: boolean;
}

export interface PreferredZone {
  zoneId: string;
  zoneName: string;
  city: string;
  priority: number; // 1 = highest priority
  isActive: boolean;
}

// Profile interfaces
export interface RiderProfile {
  id: string;
  userId: string;
  personalInfo: {
    firstName: string;
    lastName: string;
    email: string;
    phone: string;
    cnic: string;
    dateOfBirth: string;
    address: string;
    city: string;
    profilePicture?: string;
  };
  riderInfo: {
    riderCode: string;
    joinedDate: string;
    status: 'active' | 'inactive' | 'suspended' | 'pending_verification';
    rating: number;
    totalDeliveries: number;
    completionRate: number;
    onTimeRate: number;
    preferredZones: PreferredZone[];
    workingHours: {
      start: string; // HH:mm format
      end: string;   // HH:mm format
      workingDays: number[]; // 0-6, Sunday = 0
    };
  };
  documents: Document[];
  vehicles: Vehicle[];
  paymentMethods: PaymentMethod[];
  emergencyContact: {
    name: string;
    relationship: string;
    phone: string;
  };
  createdAt: string;
  updatedAt: string;
}

// Settings interfaces
export interface NotificationSettings {
  [NotificationType.ORDER_REQUESTS]: boolean;
  [NotificationType.EARNINGS_UPDATES]: boolean;
  [NotificationType.PROMOTIONS]: boolean;
  [NotificationType.SYSTEM_UPDATES]: boolean;
  [NotificationType.RATING_FEEDBACK]: boolean;
  [NotificationType.PAYMENT_UPDATES]: boolean;
  pushNotifications: boolean;
  smsNotifications: boolean;
  emailNotifications: boolean;
  soundEnabled: boolean;
  vibrationEnabled: boolean;
}

export interface PrivacySettings {
  [PrivacySetting.LOCATION_SHARING]: boolean;
  [PrivacySetting.CONTACT_VISIBILITY]: boolean;
  [PrivacySetting.RATING_VISIBILITY]: boolean;
  [PrivacySetting.EARNINGS_VISIBILITY]: boolean;
  dataCollection: boolean;
  analyticsSharing: boolean;
  marketingCommunications: boolean;
}

export interface AppSettings {
  language: Language;
  theme: 'light' | 'dark' | 'system';
  mapStyle: 'standard' | 'satellite' | 'terrain';
  distanceUnit: 'km' | 'miles';
  currency: 'PKR' | 'USD';
  autoAcceptOrders: boolean;
  autoAcceptRadius: number; // in km
  offlineMode: boolean;
  dataUsageOptimization: boolean;
}

export interface UserSettings {
  id: string;
  userId: string;
  notifications: NotificationSettings;
  privacy: PrivacySettings;
  app: AppSettings;
  updatedAt: string;
}

// State interfaces
export interface ProfileState {
  profile: RiderProfile | null;
  settings: UserSettings | null;
  availableZones: Zone[];
  isLoading: boolean;
  error: string | null;
  uploadProgress: { [key: string]: number };
}

// Context type
export interface ProfileContextType {
  state: ProfileState;
  
  // Profile functions
  fetchProfile: () => Promise<void>;
  updateProfile: (updates: Partial<RiderProfile>) => Promise<void>;
  updatePersonalInfo: (info: Partial<RiderProfile['personalInfo']>) => Promise<void>;
  updateRiderInfo: (info: Partial<RiderProfile['riderInfo']>) => Promise<void>;
  uploadProfilePicture: (imageUri: string) => Promise<string>;
  
  // Document functions
  uploadDocument: (type: Document['type'], imageUri: string) => Promise<void>;
  updateDocument: (documentId: string, updates: Partial<Document>) => Promise<void>;
  deleteDocument: (documentId: string) => Promise<void>;
  
  // Vehicle functions
  addVehicle: (vehicle: Omit<Vehicle, 'id' | 'createdAt' | 'updatedAt'>) => Promise<void>;
  updateVehicle: (vehicleId: string, updates: Partial<Vehicle>) => Promise<void>;
  deleteVehicle: (vehicleId: string) => Promise<void>;
  setActiveVehicle: (vehicleId: string) => Promise<void>;
  
  // Payment method functions
  addPaymentMethod: (method: Omit<PaymentMethod, 'id' | 'createdAt' | 'updatedAt'>) => Promise<void>;
  updatePaymentMethod: (methodId: string, updates: Partial<PaymentMethod>) => Promise<void>;
  deletePaymentMethod: (methodId: string) => Promise<void>;
  setDefaultPaymentMethod: (methodId: string) => Promise<void>;
  
  // Zone functions
  fetchAvailableZones: () => Promise<void>;
  updatePreferredZones: (zones: PreferredZone[]) => Promise<void>;
  
  // Settings functions
  fetchSettings: () => Promise<void>;
  updateNotificationSettings: (settings: Partial<NotificationSettings>) => Promise<void>;
  updatePrivacySettings: (settings: Partial<PrivacySettings>) => Promise<void>;
  updateAppSettings: (settings: Partial<AppSettings>) => Promise<void>;
  
  // Account functions
  changePassword: (currentPassword: string, newPassword: string) => Promise<void>;
  deactivateAccount: (reason: string) => Promise<void>;
  deleteAccount: (password: string) => Promise<void>;
  logout: () => Promise<void>;
  
  // Utility functions
  clearError: () => void;
  getDocumentStatus: (type: Document['type']) => DocumentStatus;
  getActiveVehicle: () => Vehicle | null;
  getDefaultPaymentMethod: () => PaymentMethod | null;
}

// Form interfaces for screens
export interface PersonalInfoForm {
  firstName: string;
  lastName: string;
  email: string;
  phone: string;
  address: string;
  city: string;
}

export interface VehicleForm {
  type: VehicleType;
  make: string;
  model: string;
  year: string;
  color: string;
  plateNumber: string;
  engineNumber?: string;
  chassisNumber?: string;
}

export interface BankAccountForm {
  bankName: string;
  accountTitle: string;
  accountNumber: string;
  iban: string;
  branchCode?: string;
  branchName?: string;
}

export interface MobileWalletForm {
  type: PaymentMethodType;
  phoneNumber: string;
  accountName: string;
}

// API response interfaces
export interface DocumentUploadResponse {
  documentId: string;
  url: string;
  status: DocumentStatus;
}

export interface ProfileUpdateResponse {
  success: boolean;
  profile: RiderProfile;
  message?: string;
}

export interface SettingsUpdateResponse {
  success: boolean;
  settings: UserSettings;
  message?: string;
}
