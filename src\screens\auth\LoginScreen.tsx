import React, { useState, useRef, useEffect } from 'react';
import {
  View,
  Text,
  ScrollView,
  KeyboardAvoidingView,
  Platform,
  Alert,
  Modal,
  TouchableOpacity,
  Animated,
  Dimensions,
} from 'react-native';
import { SafeAreaView } from 'react-native-safe-area-context';
import { Ionicons } from '@expo/vector-icons';
import { LinearGradient } from 'expo-linear-gradient';
import { Button, Input, Card, LoadingSpinner } from '../../components/ui';
import { useAuth } from '../../context/AuthContext';
import { isValidEmail } from '../../utils/helpers';
import { VALIDATION_RULES } from '../../utils/constants';
import DemoCredentialsScreen from './DemoCredentialsScreen';

const { width } = Dimensions.get('window');

interface LoginScreenProps {
  navigation?: any;
}

const LoginScreen: React.FC<LoginScreenProps> = ({ navigation }) => {
  const { state, login, clearError } = useAuth();
  const [formData, setFormData] = useState({
    email: '',
    password: '',
    rememberMe: false,
  });
  const [errors, setErrors] = useState<Record<string, string>>({});
  const [showPassword, setShowPassword] = useState(false);
  const [showDemoCredentials, setShowDemoCredentials] = useState(false);
  
  // Animation refs
  const logoScaleAnim = useRef(new Animated.Value(0.8)).current;
  const logoRotateAnim = useRef(new Animated.Value(0)).current;
  const fadeAnim = useRef(new Animated.Value(0)).current;
  const slideAnim = useRef(new Animated.Value(50)).current;
  const buttonScaleAnim = useRef(new Animated.Value(1)).current;
  const eyeScaleAnim = useRef(new Animated.Value(1)).current;
  const shakeAnim = useRef(new Animated.Value(0)).current;

  // Initialize animations
  useEffect(() => {
    Animated.parallel([
      Animated.timing(fadeAnim, {
        toValue: 1,
        duration: 800,
        useNativeDriver: true,
      }),
      Animated.spring(logoScaleAnim, {
        toValue: 1,
        tension: 50,
        friction: 7,
        useNativeDriver: true,
      }),
      Animated.timing(slideAnim, {
        toValue: 0,
        duration: 600,
        useNativeDriver: true,
      }),
    ]).start();

    // Continuous logo rotation
    const rotateAnimation = Animated.loop(
      Animated.timing(logoRotateAnim, {
        toValue: 1,
        duration: 10000,
        useNativeDriver: true,
      })
    );
    rotateAnimation.start();

    return () => rotateAnimation.stop();
  }, []);

  const validateForm = (): boolean => {
    const newErrors: Record<string, string> = {};

    // Email validation
    if (!formData.email.trim()) {
      newErrors.email = 'Email is required';
    } else if (!isValidEmail(formData.email)) {
      newErrors.email = 'Please enter a valid email address';
    }

    // Password validation
    if (!formData.password) {
      newErrors.password = 'Password is required';
    } else if (formData.password.length < VALIDATION_RULES.PASSWORD_MIN_LENGTH) {
      newErrors.password = `Password must be at least ${VALIDATION_RULES.PASSWORD_MIN_LENGTH} characters`;
    }

    setErrors(newErrors);

    // Shake animation on validation error
    if (Object.keys(newErrors).length > 0) {
      Animated.sequence([
        Animated.timing(shakeAnim, { toValue: 10, duration: 100, useNativeDriver: true }),
        Animated.timing(shakeAnim, { toValue: -10, duration: 100, useNativeDriver: true }),
        Animated.timing(shakeAnim, { toValue: 10, duration: 100, useNativeDriver: true }),
        Animated.timing(shakeAnim, { toValue: 0, duration: 100, useNativeDriver: true }),
      ]).start();
    }

    return Object.keys(newErrors).length === 0;
  };

  const handleLogin = async () => {
    // Button press animation
    Animated.sequence([
      Animated.timing(buttonScaleAnim, { toValue: 0.95, duration: 100, useNativeDriver: true }),
      Animated.timing(buttonScaleAnim, { toValue: 1, duration: 100, useNativeDriver: true }),
    ]).start();

    if (!validateForm()) {
      return;
    }

    try {
      clearError();
      const credentials = {
        email: formData.email.trim().toLowerCase(),
        password: formData.password,
        rememberMe: formData.rememberMe,
      };

      await login(credentials);
    } catch (error: any) {
      Alert.alert('Login Failed', error.message);
    }
  };

  const handleInputChange = (field: string, value: string) => {
    setFormData(prev => ({ ...prev, [field]: value }));
    
    // Clear field error when user starts typing
    if (errors[field]) {
      setErrors(prev => ({ ...prev, [field]: '' }));
    }
  };

  const handlePasswordToggle = () => {
    // Eye icon animation
    Animated.sequence([
      Animated.timing(eyeScaleAnim, { toValue: 0.8, duration: 100, useNativeDriver: true }),
      Animated.timing(eyeScaleAnim, { toValue: 1, duration: 100, useNativeDriver: true }),
    ]).start();
    
    setShowPassword(!showPassword);
  };

  const handleForgotPassword = () => {
    Alert.alert(
      'Forgot Password',
      'Please contact support to reset your password.',
      [{ text: 'OK' }]
    );
  };

  const handleSocialLogin = (provider: 'google' | 'facebook') => {
    Alert.alert(
      'Social Login',
      `${provider} login will be implemented in a future update.`,
      [{ text: 'OK' }]
    );
  };



  // Animation interpolations
  const logoRotateInterpolate = logoRotateAnim.interpolate({
    inputRange: [0, 1],
    outputRange: ['0deg', '360deg'],
  });

  if (state.isLoading) {
    return (
      <LinearGradient
        colors={['#FEF3C7', '#FDE68A', '#F59E0B']}
        style={{ flex: 1 }}
      >
        <SafeAreaView style={{ flex: 1 }}>
          <LoadingSpinner message="Signing you in..." />
        </SafeAreaView>
      </LinearGradient>
    );
  }

  return (
    <LinearGradient
      colors={['#FEF3C7', '#FDE68A', '#F59E0B']}
      style={{ flex: 1 }}
    >
      <SafeAreaView style={{ flex: 1 }}>
        <KeyboardAvoidingView
          style={{ flex: 1 }}
          behavior={Platform.OS === 'ios' ? 'padding' : 'height'}
        >
          <ScrollView
            contentContainerStyle={{
              flexGrow: 1,
              justifyContent: 'center',
              padding: 20,
            }}
            keyboardShouldPersistTaps="handled"
            showsVerticalScrollIndicator={false}
          >
            {/* Animated Header */}
            <Animated.View 
              style={{ 
                alignItems: 'center', 
                marginBottom: 40,
                opacity: fadeAnim,
                transform: [{ translateY: slideAnim }],
              }}
            >
              {/* Animated Logo */}
              <Animated.View
                style={{
                  width: 100,
                  height: 100,
                  backgroundColor: 'white',
                  borderRadius: 50,
                  justifyContent: 'center',
                  alignItems: 'center',
                  marginBottom: 20,
                  shadowColor: '#000',
                  shadowOffset: { width: 0, height: 8 },
                  shadowOpacity: 0.15,
                  shadowRadius: 16,
                  elevation: 8,
                  transform: [
                    { scale: logoScaleAnim },
                    { rotate: logoRotateInterpolate },
                  ],
                }}
              >
                <Ionicons name="bicycle" size={48} color="#F97316" />
              </Animated.View>
              
              <Text style={{
                fontSize: 32,
                fontWeight: 'bold',
                color: '#1F2937',
                marginBottom: 8,
                textAlign: 'center',
              }}>
                Welcome Back
              </Text>
              
              <Text style={{
                fontSize: 16,
                color: '#6B7280',
                textAlign: 'center',
                marginBottom: 8,
              }}>
                Sign in to your FoodWay Rider account
              </Text>


            </Animated.View>

            {/* Login Form */}
            <Animated.View
              style={{
                transform: [{ translateX: shakeAnim }],
              }}
            >
              <Card variant="elevated" padding="lg">
                {/* Email Input Field */}
                <Input
                  label="Email Address"
                  placeholder="Enter your email"
                  value={formData.email}
                  onChangeText={(value) => handleInputChange('email', value)}
                  error={errors.email}
                  leftIcon="mail-outline"
                  keyboardType="email-address"
                  autoCapitalize="none"
                  autoCorrect={false}
                  required
                />

                {/* Password Input with Animated Eye Icon */}
                <Input
                  label="Password"
                  placeholder="Enter your password"
                  value={formData.password}
                  onChangeText={(value) => handleInputChange('password', value)}
                  error={errors.password}
                  leftIcon="lock-closed-outline"
                  rightIcon={showPassword ? "eye-off-outline" : "eye-outline"}
                  onRightIconPress={handlePasswordToggle}
                  secureTextEntry={!showPassword}
                  required
                />

                {/* Error Display */}
                {state.error && (
                  <View style={{
                    backgroundColor: '#FEE2E2',
                    padding: 12,
                    borderRadius: 12,
                    marginBottom: 16,
                    borderLeftWidth: 4,
                    borderLeftColor: '#EF4444',
                  }}>
                    <View style={{ flexDirection: 'row', alignItems: 'center' }}>
                      <Ionicons name="alert-circle" size={16} color="#DC2626" />
                      <Text style={{ 
                        color: '#DC2626', 
                        fontSize: 14, 
                        marginLeft: 8,
                        flex: 1,
                      }}>
                        {state.error}
                      </Text>
                    </View>
                  </View>
                )}

                {/* Animated Login Button */}
                <Animated.View style={{ transform: [{ scale: buttonScaleAnim }] }}>
                  <TouchableOpacity
                    onPress={handleLogin}
                    disabled={state.isLoading}
                    style={{
                      backgroundColor: '#F97316',
                      paddingVertical: 16,
                      borderRadius: 12,
                      marginBottom: 16,
                      flexDirection: 'row',
                      alignItems: 'center',
                      justifyContent: 'center',
                      shadowColor: '#F97316',
                      shadowOffset: { width: 0, height: 4 },
                      shadowOpacity: 0.3,
                      shadowRadius: 8,
                      elevation: 6,
                    }}
                  >
                    {state.isLoading ? (
                      <Animated.View style={{ marginRight: 8 }}>
                        <Ionicons name="refresh" size={20} color="white" />
                      </Animated.View>
                    ) : (
                      <Ionicons name="log-in-outline" size={20} color="white" style={{ marginRight: 8 }} />
                    )}
                    <Text style={{
                      color: 'white',
                      fontSize: 16,
                      fontWeight: '600',
                    }}>
                      {state.isLoading ? 'Signing In...' : 'Sign In'}
                    </Text>
                  </TouchableOpacity>
                </Animated.View>



                {/* Forgot Password */}
                <TouchableOpacity
                  onPress={handleForgotPassword}
                  style={{ alignItems: 'center', paddingVertical: 8 }}
                >
                  <Text style={{
                    color: '#F97316',
                    fontSize: 14,
                    fontWeight: '500',
                  }}>
                    Forgot Password?
                  </Text>
                </TouchableOpacity>
              </Card>
            </Animated.View>

            {/* Demo Credentials & Sign Up Section */}
            <View style={{
              marginTop: 24,
              alignItems: 'center',
            }}>
              {/* Demo Credentials Button */}
              <TouchableOpacity
                onPress={() => setShowDemoCredentials(true)}
                style={{
                  flexDirection: 'row',
                  alignItems: 'center',
                  paddingHorizontal: 20,
                  paddingVertical: 12,
                  backgroundColor: 'rgba(59, 130, 246, 0.1)',
                  borderRadius: 12,
                  marginBottom: 16,
                }}
              >
                <Ionicons name="key-outline" size={16} color="#3B82F6" />
                <Text style={{
                  color: '#3B82F6',
                  fontSize: 14,
                  fontWeight: '500',
                  marginLeft: 8,
                }}>
                  View Demo Credentials
                </Text>
              </TouchableOpacity>

              {/* Sign Up Link */}
              <View style={{
                flexDirection: 'row',
                alignItems: 'center',
              }}>
                <Text style={{
                  color: '#6B7280',
                  fontSize: 14,
                }}>
                  Don't have an account?{' '}
                </Text>
                <TouchableOpacity
                  onPress={() => navigation?.navigate('RegistrationInfo')}
                >
                  <Text style={{
                    color: '#F97316',
                    fontSize: 14,
                    fontWeight: '600',
                  }}>
                    Sign Up
                  </Text>
                </TouchableOpacity>
              </View>
            </View>
          </ScrollView>
        </KeyboardAvoidingView>

        {/* Demo Credentials Modal */}
        <Modal
          visible={showDemoCredentials}
          animationType="slide"
          presentationStyle="pageSheet"
        >
          <DemoCredentialsScreen onClose={() => setShowDemoCredentials(false)} />
        </Modal>
      </SafeAreaView>
    </LinearGradient>
  );
};

export default LoginScreen;
