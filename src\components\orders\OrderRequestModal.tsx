import React, { useState, useEffect, useRef } from 'react';
import {
  View,
  Text,
  Modal,
  TouchableOpacity,
  Dimensions,
  Alert,
  Vibration,
  Animated,
  ImageBackground,
  StatusBar,
} from 'react-native';
import { Ionicons } from '@expo/vector-icons';
import { LinearGradient } from 'expo-linear-gradient';
import { BlurView } from 'expo-blur';
import { Card, Button, Badge } from '../ui';
import { useOrders } from '../../context/OrderContext';
import { Order } from '../../types/orders';
import { formatCurrency, formatDistance, formatDuration } from '../../utils/helpers';

interface OrderRequestModalProps {
  visible: boolean;
  onClose: () => void;
  orders: Order[];
}

const { width, height } = Dimensions.get('window');

const OrderRequestModal: React.FC<OrderRequestModalProps> = ({
  visible,
  onClose,
  orders,
}) => {
  const { acceptOrder, declineOrder } = useOrders();
  const [currentOrderIndex, setCurrentOrderIndex] = useState(0);
  const [countdown, setCountdown] = useState(30); // 30 seconds to respond
  const [isProcessing, setIsProcessing] = useState(false);

  // Animation refs
  const slideAnimation = useRef(new Animated.Value(0)).current;
  const countdownAnimation = useRef(new Animated.Value(1)).current;
  const pulseAnimation = useRef(new Animated.Value(1)).current;
  const mapAnimation = useRef(new Animated.Value(0)).current;
  const buttonAnimation = useRef(new Animated.Value(0)).current;

  const currentOrder = orders[currentOrderIndex];

  useEffect(() => {
    if (visible && currentOrder) {
      setCountdown(30);
      // Vibrate to alert rider
      Vibration.vibrate([0, 500, 200, 500]);

      // Initialize animations
      Animated.parallel([
        Animated.timing(slideAnimation, {
          toValue: 1,
          duration: 500,
          useNativeDriver: true,
        }),
        Animated.stagger(200, [
          Animated.timing(mapAnimation, {
            toValue: 1,
            duration: 800,
            useNativeDriver: true,
          }),
          Animated.timing(buttonAnimation, {
            toValue: 1,
            duration: 600,
            useNativeDriver: true,
          }),
        ]),
      ]).start();

      // Start pulse animation for urgent countdown
      const startPulse = () => {
        Animated.sequence([
          Animated.timing(pulseAnimation, {
            toValue: 1.1,
            duration: 500,
            useNativeDriver: true,
          }),
          Animated.timing(pulseAnimation, {
            toValue: 1,
            duration: 500,
            useNativeDriver: true,
          }),
        ]).start(() => {
          if (countdown <= 10) startPulse();
        });
      };

      const timer = setInterval(() => {
        setCountdown((prev) => {
          if (prev <= 1) {
            // Auto-decline when countdown reaches 0
            setTimeout(() => handleDecline(), 0); // Use setTimeout to avoid state update during render
            return 0;
          }
          if (prev <= 10 && prev === 10) {
            startPulse(); // Start pulsing when countdown reaches 10
          }
          return prev - 1;
        });
      }, 1000);

      return () => {
        clearInterval(timer);
        slideAnimation.setValue(0);
        mapAnimation.setValue(0);
        buttonAnimation.setValue(0);
        pulseAnimation.setValue(1);
      };
    }
  }, [visible, currentOrder]);

  const handleAccept = async () => {
    if (!currentOrder || isProcessing) return;
    
    setIsProcessing(true);
    try {
      await acceptOrder(currentOrder.id);
      Alert.alert(
        'Order Accepted!',
        'Navigate to the restaurant to pick up the order.',
        [{ text: 'OK', onPress: onClose }]
      );
    } catch (error: any) {
      Alert.alert('Error', error.message);
    } finally {
      setIsProcessing(false);
    }
  };

  const handleDecline = async () => {
    if (!currentOrder || isProcessing) return;
    
    setIsProcessing(true);
    try {
      await declineOrder(currentOrder.id);
      
      // Show next order if available
      if (currentOrderIndex < orders.length - 1) {
        setCurrentOrderIndex(prev => prev + 1);
      } else {
        onClose();
      }
    } catch (error: any) {
      Alert.alert('Error', error.message);
    } finally {
      setIsProcessing(false);
    }
  };

  const renderCountdownRing = () => {
    const progress = countdown / 30; // Progress from 1 to 0
    const circumference = 2 * Math.PI * 35; // radius = 35
    const strokeDashoffset = circumference * (1 - progress);

    return (
      <Animated.View style={{
        width: 80,
        height: 80,
        justifyContent: 'center',
        alignItems: 'center',
        transform: [{ scale: countdown <= 10 ? pulseAnimation : 1 }],
      }}>
        {/* Background Circle */}
        <View style={{
          position: 'absolute',
          width: 80,
          height: 80,
          borderRadius: 40,
          backgroundColor: countdown <= 10 ? '#fee2e2' : '#f0f9ff',
          opacity: 0.3,
        }} />

        {/* Progress Ring - Simulated with border */}
        <View style={{
          position: 'absolute',
          width: 70,
          height: 70,
          borderRadius: 35,
          borderWidth: 4,
          borderColor: countdown <= 10 ? '#dc2626' : '#0ea5e9',
          borderTopColor: 'transparent',
          borderRightColor: progress > 0.75 ? (countdown <= 10 ? '#dc2626' : '#0ea5e9') : 'transparent',
          borderBottomColor: progress > 0.5 ? (countdown <= 10 ? '#dc2626' : '#0ea5e9') : 'transparent',
          borderLeftColor: progress > 0.25 ? (countdown <= 10 ? '#dc2626' : '#0ea5e9') : 'transparent',
          transform: [{ rotate: '-90deg' }],
        }} />

        {/* Countdown Text */}
        <View style={{
          justifyContent: 'center',
          alignItems: 'center',
        }}>
          <Text style={{
            fontSize: 20,
            fontWeight: 'bold',
            color: countdown <= 10 ? '#dc2626' : '#0ea5e9',
          }}>
            {countdown}
          </Text>
          <Text style={{
            fontSize: 10,
            color: countdown <= 10 ? '#dc2626' : '#0ea5e9',
            fontWeight: '500',
          }}>
            SEC
          </Text>
        </View>
      </Animated.View>
    );
  };

  const renderRoutePreview = () => {
    return (
      <Animated.View style={{
        opacity: mapAnimation,
        transform: [{
          translateY: mapAnimation.interpolate({
            inputRange: [0, 1],
            outputRange: [20, 0],
          }),
        }],
        marginVertical: 16,
      }}>
        <Text style={{
          fontSize: 16,
          fontWeight: 'bold',
          color: '#111827',
          marginBottom: 12,
        }}>
          📍 Route Preview
        </Text>

        <View style={{
          height: 120,
          borderRadius: 12,
          overflow: 'hidden',
          backgroundColor: '#f3f4f6',
        }}>
          <ImageBackground
            source={{ uri: 'https://images.unsplash.com/photo-1524661135-423995f22d0b?w=400&h=200&fit=crop' }}
            style={{ flex: 1 }}
            blurRadius={1}
          >
            <BlurView intensity={10} style={{ flex: 1, justifyContent: 'center', alignItems: 'center' }}>
              {/* Route Line Simulation */}
              <View style={{
                position: 'absolute',
                top: 30,
                left: 30,
                right: 30,
                height: 3,
                backgroundColor: '#f97316',
                borderRadius: 2,
              }} />

              {/* Restaurant Pin */}
              <View style={{
                position: 'absolute',
                top: 20,
                left: 25,
                backgroundColor: '#10b981',
                borderRadius: 15,
                width: 30,
                height: 30,
                justifyContent: 'center',
                alignItems: 'center',
                borderWidth: 3,
                borderColor: 'white',
              }}>
                <Ionicons name="restaurant" size={14} color="white" />
              </View>

              {/* Customer Pin */}
              <View style={{
                position: 'absolute',
                top: 20,
                right: 25,
                backgroundColor: '#dc2626',
                borderRadius: 15,
                width: 30,
                height: 30,
                justifyContent: 'center',
                alignItems: 'center',
                borderWidth: 3,
                borderColor: 'white',
              }}>
                <Ionicons name="home" size={14} color="white" />
              </View>

              {/* Distance and ETA */}
              <View style={{
                backgroundColor: 'rgba(0,0,0,0.7)',
                paddingHorizontal: 12,
                paddingVertical: 6,
                borderRadius: 20,
                marginTop: 20,
              }}>
                <Text style={{ color: 'white', fontSize: 12, fontWeight: '600' }}>
                  {formatDistance(currentOrder.estimatedDistance)} • {formatDuration(currentOrder.estimatedDuration)}
                </Text>
              </View>
            </BlurView>
          </ImageBackground>
        </View>
      </Animated.View>
    );
  };

  const renderHeatmapIndicator = () => {
    // Simulate busy area indicator based on order location
    const isBusyArea = Math.random() > 0.5;

    return (
      <View style={{
        flexDirection: 'row',
        alignItems: 'center',
        backgroundColor: isBusyArea ? '#fef3c7' : '#f3f4f6',
        paddingHorizontal: 12,
        paddingVertical: 6,
        borderRadius: 20,
        marginTop: 8,
      }}>
        <View style={{
          width: 8,
          height: 8,
          borderRadius: 4,
          backgroundColor: isBusyArea ? '#f59e0b' : '#6b7280',
          marginRight: 6,
        }} />
        <Text style={{
          fontSize: 12,
          color: isBusyArea ? '#92400e' : '#6b7280',
          fontWeight: '500',
        }}>
          {isBusyArea ? 'Busy Area - High Demand 🔥' : 'Normal Area'}
        </Text>
      </View>
    );
  };

  if (!visible || !currentOrder) {
    return null;
  }

  return (
    <Modal
      visible={visible}
      transparent
      animationType="none"
      onRequestClose={onClose}
    >
      <StatusBar barStyle="light-content" backgroundColor="rgba(0,0,0,0.8)" />
      <View style={{
        flex: 1,
        backgroundColor: 'rgba(0, 0, 0, 0.8)',
        justifyContent: 'center',
        paddingHorizontal: 20,
      }}>
        <Animated.View style={{
          opacity: slideAnimation,
          transform: [{
            scale: slideAnimation.interpolate({
              inputRange: [0, 1],
              outputRange: [0.8, 1],
            }),
          }],
        }}>
          <LinearGradient
            colors={['#ffffff', '#f8fafc']}
            style={{
              borderRadius: 24,
              overflow: 'hidden',
              shadowColor: '#000',
              shadowOffset: { width: 0, height: 20 },
              shadowOpacity: 0.3,
              shadowRadius: 30,
              elevation: 20,
            }}
          >
            {/* Header with countdown ring */}
            <View style={{
              flexDirection: 'row',
              justifyContent: 'space-between',
              alignItems: 'center',
              paddingHorizontal: 24,
              paddingTop: 24,
              paddingBottom: 16,
            }}>
              <View style={{ flex: 1 }}>
                <Text style={{
                  fontSize: 24,
                  fontWeight: 'bold',
                  color: '#111827',
                  marginBottom: 4,
                }}>
                  🚨 New Order Request
                </Text>
                <Text style={{
                  fontSize: 14,
                  color: '#6b7280',
                  fontWeight: '500',
                }}>
                  Respond quickly to earn more!
                </Text>
              </View>

              {renderCountdownRing()}
            </View>

            {/* Order Details */}
            <View style={{ paddingHorizontal: 24 }}>
              {/* Restaurant Info */}
              <View style={{
                flexDirection: 'row',
                alignItems: 'center',
                marginBottom: 16,
                backgroundColor: 'white',
                padding: 16,
                borderRadius: 16,
                shadowColor: '#000',
                shadowOffset: { width: 0, height: 2 },
                shadowOpacity: 0.1,
                shadowRadius: 8,
                elevation: 4,
              }}>
                <LinearGradient
                  colors={['#f97316', '#fb923c']}
                  style={{
                    width: 60,
                    height: 60,
                    borderRadius: 30,
                    justifyContent: 'center',
                    alignItems: 'center',
                    marginRight: 16,
                  }}
                >
                  <Ionicons name="restaurant" size={28} color="white" />
                </LinearGradient>

                <View style={{ flex: 1 }}>
                  <Text style={{
                    fontSize: 20,
                    fontWeight: 'bold',
                    color: '#111827',
                    marginBottom: 4,
                  }}>
                    {currentOrder.restaurant.name}
                  </Text>
                  <Text style={{
                    fontSize: 14,
                    color: '#6b7280',
                    marginBottom: 8,
                  }}>
                    📍 {currentOrder.restaurant.address.street}
                  </Text>
                  {renderHeatmapIndicator()}
                </View>
              </View>

              {/* Earnings Highlight */}
              <LinearGradient
                colors={['#10b981', '#059669']}
                style={{
                  padding: 20,
                  borderRadius: 16,
                  marginBottom: 16,
                  alignItems: 'center',
                }}
              >
                <Text style={{
                  fontSize: 16,
                  color: 'white',
                  fontWeight: '600',
                  marginBottom: 8,
                }}>
                  💰 Your Earnings
                </Text>
                <Text style={{
                  fontSize: 32,
                  fontWeight: 'bold',
                  color: 'white',
                  marginBottom: 4,
                }}>
                  {formatCurrency(currentOrder.estimatedEarnings)}
                </Text>
                <Text style={{
                  fontSize: 14,
                  color: 'rgba(255,255,255,0.8)',
                  fontWeight: '500',
                }}>
                  Order Total: {formatCurrency(currentOrder.total)}
                </Text>
              </LinearGradient>

              {/* Quick Stats */}
              <View style={{
                flexDirection: 'row',
                marginBottom: 16,
                gap: 12,
              }}>
                <View style={{
                  flex: 1,
                  backgroundColor: 'white',
                  padding: 16,
                  borderRadius: 12,
                  alignItems: 'center',
                  shadowColor: '#000',
                  shadowOffset: { width: 0, height: 2 },
                  shadowOpacity: 0.1,
                  shadowRadius: 4,
                  elevation: 2,
                }}>
                  <Ionicons name="location" size={20} color="#f97316" />
                  <Text style={{
                    fontSize: 16,
                    fontWeight: 'bold',
                    color: '#111827',
                    marginTop: 4,
                  }}>
                    {formatDistance(currentOrder.estimatedDistance)}
                  </Text>
                  <Text style={{
                    fontSize: 12,
                    color: '#6b7280',
                    fontWeight: '500',
                  }}>
                    Distance
                  </Text>
                </View>

                <View style={{
                  flex: 1,
                  backgroundColor: 'white',
                  padding: 16,
                  borderRadius: 12,
                  alignItems: 'center',
                  shadowColor: '#000',
                  shadowOffset: { width: 0, height: 2 },
                  shadowOpacity: 0.1,
                  shadowRadius: 4,
                  elevation: 2,
                }}>
                  <Ionicons name="time" size={20} color="#f97316" />
                  <Text style={{
                    fontSize: 16,
                    fontWeight: 'bold',
                    color: '#111827',
                    marginTop: 4,
                  }}>
                    {formatDuration(currentOrder.estimatedDuration)}
                  </Text>
                  <Text style={{
                    fontSize: 12,
                    color: '#6b7280',
                    fontWeight: '500',
                  }}>
                    ETA
                  </Text>
                </View>
              </View>

              {/* Route Preview */}
              {renderRoutePreview()}

              {/* Delivery Info */}
              <View style={{
                backgroundColor: 'white',
                padding: 16,
                borderRadius: 12,
                marginBottom: 16,
                shadowColor: '#000',
                shadowOffset: { width: 0, height: 2 },
                shadowOpacity: 0.1,
                shadowRadius: 4,
                elevation: 2,
              }}>
                <Text style={{
                  fontSize: 16,
                  fontWeight: 'bold',
                  color: '#111827',
                  marginBottom: 12,
                }}>
                  🏠 Delivery Address
                </Text>
                <Text style={{
                  fontSize: 14,
                  color: '#6b7280',
                  lineHeight: 20,
                }}>
                  {currentOrder.deliveryAddress.street}, {currentOrder.deliveryAddress.city}
                </Text>
              </View>
            </View>

            {/* Action Buttons */}
            <Animated.View style={{
              flexDirection: 'row',
              paddingHorizontal: 24,
              paddingBottom: 24,
              gap: 16,
              opacity: buttonAnimation,
              transform: [{
                translateY: buttonAnimation.interpolate({
                  inputRange: [0, 1],
                  outputRange: [30, 0],
                }),
              }],
            }}>
              <TouchableOpacity
                onPress={handleDecline}
                disabled={isProcessing}
                style={{
                  flex: 1,
                  backgroundColor: '#fee2e2',
                  paddingVertical: 16,
                  borderRadius: 16,
                  alignItems: 'center',
                  borderWidth: 2,
                  borderColor: '#dc2626',
                  opacity: isProcessing ? 0.6 : 1,
                }}
              >
                <Ionicons name="close-circle" size={24} color="#dc2626" />
                <Text style={{
                  fontSize: 16,
                  fontWeight: 'bold',
                  color: '#dc2626',
                  marginTop: 4,
                }}>
                  Decline
                </Text>
              </TouchableOpacity>

              <TouchableOpacity
                onPress={handleAccept}
                disabled={isProcessing}
                style={{
                  flex: 2,
                  opacity: isProcessing ? 0.6 : 1,
                }}
              >
                <LinearGradient
                  colors={['#10b981', '#059669']}
                  style={{
                    paddingVertical: 16,
                    borderRadius: 16,
                    alignItems: 'center',
                    shadowColor: '#10b981',
                    shadowOffset: { width: 0, height: 4 },
                    shadowOpacity: 0.3,
                    shadowRadius: 8,
                    elevation: 8,
                  }}
                >
                  <Ionicons name="checkmark-circle" size={24} color="white" />
                  <Text style={{
                    fontSize: 18,
                    fontWeight: 'bold',
                    color: 'white',
                    marginTop: 4,
                  }}>
                    Accept Order 🚀
                  </Text>
                </LinearGradient>
              </TouchableOpacity>
            </Animated.View>
          </LinearGradient>
        </Animated.View>
      </View>
    </Modal>
  );
};

export default OrderRequestModal;
