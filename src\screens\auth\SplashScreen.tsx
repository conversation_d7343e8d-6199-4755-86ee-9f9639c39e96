import React, { useEffect, useRef } from 'react';
import { View, Text, Animated, Dimensions } from 'react-native';
import { SafeAreaView } from 'react-native-safe-area-context';
import { Ionicons } from '@expo/vector-icons';
import { LinearGradient } from 'expo-linear-gradient';

const { width, height } = Dimensions.get('window');

interface SplashScreenProps {
  onFinish: () => void;
}

const SplashScreen: React.FC<SplashScreenProps> = ({ onFinish }) => {
  const fadeAnim = useRef(new Animated.Value(0)).current;
  const scaleAnim = useRef(new Animated.Value(0.3)).current;
  const slideAnim = useRef(new Animated.Value(50)).current;
  const progressAnim = useRef(new Animated.Value(0)).current;
  const rotateAnim = useRef(new Animated.Value(0)).current;
  const pulseAnim = useRef(new Animated.Value(1)).current;
  const spinAnim = useRef(new Animated.Value(0)).current;

  useEffect(() => {
    // Continuous motorbike rotation animation
    const rotateAnimation = Animated.loop(
      Animated.timing(rotateAnim, {
        toValue: 1,
        duration: 3000,
        useNativeDriver: true,
      })
    );

    // Continuous pulse animation for motorbike
    const pulseAnimation = Animated.loop(
      Animated.sequence([
        Animated.timing(pulseAnim, {
          toValue: 1.2,
          duration: 1000,
          useNativeDriver: true,
        }),
        Animated.timing(pulseAnim, {
          toValue: 1,
          duration: 1000,
          useNativeDriver: true,
        }),
      ])
    );

    // Loading spinner animation
    const spinAnimation = Animated.loop(
      Animated.timing(spinAnim, {
        toValue: 1,
        duration: 1000,
        useNativeDriver: true,
      })
    );

    // Start continuous animations
    rotateAnimation.start();
    pulseAnimation.start();
    spinAnimation.start();

    // Start main animation sequence
    const animationSequence = Animated.sequence([
      // Logo animation
      Animated.parallel([
        Animated.timing(fadeAnim, {
          toValue: 1,
          duration: 800,
          useNativeDriver: true,
        }),
        Animated.spring(scaleAnim, {
          toValue: 1,
          tension: 50,
          friction: 7,
          useNativeDriver: true,
        }),
      ]),
      // Text slide up
      Animated.timing(slideAnim, {
        toValue: 0,
        duration: 600,
        useNativeDriver: true,
      }),
      // Progress bar
      Animated.timing(progressAnim, {
        toValue: 1,
        duration: 1500,
        useNativeDriver: false,
      }),
    ]);

    animationSequence.start(() => {
      // Wait a bit then finish
      setTimeout(() => {
        onFinish();
      }, 500);
    });

    // Cleanup function to stop animations
    return () => {
      rotateAnimation.stop();
      pulseAnimation.stop();
      spinAnimation.stop();
    };
  }, [fadeAnim, scaleAnim, slideAnim, progressAnim, rotateAnim, pulseAnim, spinAnim, onFinish]);

  // Animation interpolations
  const rotateInterpolate = rotateAnim.interpolate({
    inputRange: [0, 1],
    outputRange: ['0deg', '360deg'],
  });

  const spinInterpolate = spinAnim.interpolate({
    inputRange: [0, 1],
    outputRange: ['0deg', '360deg'],
  });

  return (
    <LinearGradient
      colors={['#DC2626', '#EA580C', '#F97316']} // Red to orange gradient
      style={{ flex: 1 }}
      start={{ x: 0, y: 0 }}
      end={{ x: 1, y: 1 }}
    >
      <SafeAreaView style={{ flex: 1 }}>
        <View style={{
          flex: 1,
          justifyContent: 'center',
          alignItems: 'center',
          paddingHorizontal: 32,
          minHeight: height,
        }}>
          {/* Logo Container */}
          <Animated.View
            style={{
              opacity: fadeAnim,
              transform: [{ scale: scaleAnim }],
              alignItems: 'center',
              marginBottom: 32,
            }}
          >
            {/* Animated Motorbike Icon */}
            <View style={{
              width: 120,
              height: 120,
              backgroundColor: 'white',
              borderRadius: 60,
              alignItems: 'center',
              justifyContent: 'center',
              marginBottom: 16,
              shadowColor: '#000',
              shadowOffset: { width: 0, height: 8 },
              shadowOpacity: 0.3,
              shadowRadius: 16,
              elevation: 8,
            }}>
              <Animated.View
                style={{
                  transform: [
                    { rotate: rotateInterpolate },
                    { scale: pulseAnim },
                  ],
                }}
              >
                <Ionicons name="bicycle" size={64} color="#F97316" />
              </Animated.View>
            </View>

            {/* App Name */}
            <Text style={{
              fontSize: 48,
              fontWeight: 'bold',
              color: 'white',
              marginBottom: 8,
              textAlign: 'center',
              textShadowColor: 'rgba(0, 0, 0, 0.3)',
              textShadowOffset: { width: 2, height: 2 },
              textShadowRadius: 4,
            }}>
              FoodWay
            </Text>
            <Text style={{
              fontSize: 24,
              color: 'rgba(255, 255, 255, 0.9)',
              fontWeight: '600',
              textAlign: 'center',
            }}>
              Rider
            </Text>
          </Animated.View>

          {/* Tagline */}
          <Animated.View
            style={{
              opacity: fadeAnim,
              transform: [{ translateY: slideAnim }],
              alignItems: 'center',
              marginBottom: 48,
            }}
          >
            <Text style={{
              fontSize: 20,
              color: 'white',
              textAlign: 'center',
              lineHeight: 28,
              fontWeight: '500',
              textShadowColor: 'rgba(0, 0, 0, 0.2)',
              textShadowOffset: { width: 1, height: 1 },
              textShadowRadius: 2,
            }}>
              Deliver happiness,{'\n'}earn with every ride
            </Text>
          </Animated.View>

          {/* Loading Section */}
          <Animated.View
            style={{
              opacity: fadeAnim,
              width: '100%',
              maxWidth: 320,
              alignItems: 'center',
            }}
          >
            {/* Animated Loading Spinner */}
            <Animated.View
              style={{
                transform: [{ rotate: spinInterpolate }],
                marginBottom: 16,
              }}
            >
              <Ionicons name="refresh" size={32} color="white" />
            </Animated.View>

            {/* Progress Bar */}
            <View style={{
              height: 4,
              backgroundColor: 'rgba(255, 255, 255, 0.3)',
              borderRadius: 2,
              overflow: 'hidden',
              width: '100%',
              marginBottom: 16,
            }}>
              <Animated.View
                style={{
                  width: progressAnim.interpolate({
                    inputRange: [0, 1],
                    outputRange: ['0%', '100%'],
                  }),
                  height: '100%',
                  backgroundColor: 'white',
                  borderRadius: 2,
                }}
              />
            </View>

            <Text style={{
              color: 'rgba(255, 255, 255, 0.9)',
              textAlign: 'center',
              fontSize: 16,
              fontWeight: '500',
            }}>
              Loading...
            </Text>
          </Animated.View>

          {/* Version */}
          <Animated.View
            style={{
              opacity: fadeAnim,
              position: 'absolute',
              bottom: 32,
            }}
          >
            <Text style={{
              color: 'rgba(255, 255, 255, 0.7)',
              fontSize: 14,
              textAlign: 'center',
            }}>
              Version 1.0.0
            </Text>
          </Animated.View>
        </View>
      </SafeAreaView>
    </LinearGradient>
  );
};

export default SplashScreen;
