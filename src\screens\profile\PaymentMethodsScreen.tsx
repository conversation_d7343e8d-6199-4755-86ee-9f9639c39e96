import React, { useState } from 'react';
import {
  View,
  Text,
  ScrollView,
  TouchableOpacity,
  Alert,
  Modal,
  TextInput,
} from 'react-native';
import { SafeAreaView } from 'react-native-safe-area-context';
import { Ionicons } from '@expo/vector-icons';
import { useNavigation } from '@react-navigation/native';
import { Card, Button, Badge } from '../../components/ui';
import { PaymentMethodType, PaymentMethod, PaymentMethodForm } from '../../types/profile';

// Mock payment methods data
const mockPaymentMethods: PaymentMethod[] = [
  {
    id: '1',
    type: PaymentMethodType.BANK_ACCOUNT,
    name: 'HBL Account',
    isDefault: true,
    isActive: true,
    isVerified: true,
    createdAt: '2023-01-16',
    updatedAt: '2023-01-16',
    details: {
      bankName: 'Habib Bank Limited',
      accountTitle: '<PERSON>',
      accountNumber: '***********',
      iban: 'PK36HABB00***********234',
    },
  },
  {
    id: '2',
    type: PaymentMethodType.JAZZCASH,
    name: 'JazzCash Wallet',
    isDefault: false,
    isActive: true,
    isVerified: true,
    createdAt: '2023-02-01',
    updatedAt: '2023-02-01',
    details: {
      phoneNumber: '+92 300 1234567',
      accountTitle: 'Muhammad Ahmed',
    },
  },
  {
    id: '3',
    type: PaymentMethodType.EASYPAISA,
    name: 'EasyPaisa Wallet',
    isDefault: false,
    isActive: false,
    isVerified: false,
    createdAt: '2023-02-15',
    updatedAt: '2023-02-15',
    details: {
      phoneNumber: '+92 301 2345678',
      accountTitle: 'Muhammad Ahmed',
    },
  },
];

const PaymentMethodsScreen: React.FC = () => {
  const navigation = useNavigation();
  const [paymentMethods, setPaymentMethods] = useState(mockPaymentMethods);
  const [showAddModal, setShowAddModal] = useState(false);
  const [selectedType, setSelectedType] = useState<PaymentMethodType>(PaymentMethodType.BANK_ACCOUNT);
  const [formData, setFormData] = useState<PaymentMethodForm>({
    type: PaymentMethodType.BANK_ACCOUNT,
    name: '',
    details: {},
  });

  const getPaymentMethodIcon = (type: PaymentMethodType) => {
    switch (type) {
      case PaymentMethodType.BANK_ACCOUNT:
        return 'card';
      case PaymentMethodType.JAZZCASH:
        return 'phone-portrait';
      case PaymentMethodType.EASYPAISA:
        return 'phone-portrait';
      case PaymentMethodType.SADAPAY:
        return 'phone-portrait';
      case PaymentMethodType.NAYAPAY:
        return 'phone-portrait';
      default:
        return 'card';
    }
  };

  const getPaymentMethodColor = (type: PaymentMethodType) => {
    switch (type) {
      case PaymentMethodType.BANK_ACCOUNT:
        return '#3b82f6';
      case PaymentMethodType.JAZZCASH:
        return '#f59e0b';
      case PaymentMethodType.EASYPAISA:
        return '#10b981';
      case PaymentMethodType.SADAPAY:
        return '#8b5cf6';
      case PaymentMethodType.NAYAPAY:
        return '#ef4444';
      default:
        return '#6b7280';
    }
  };

  const formatPaymentMethodName = (type: PaymentMethodType) => {
    switch (type) {
      case PaymentMethodType.BANK_ACCOUNT:
        return 'Bank Account';
      case PaymentMethodType.JAZZCASH:
        return 'JazzCash';
      case PaymentMethodType.EASYPAISA:
        return 'EasyPaisa';
      case PaymentMethodType.SADAPAY:
        return 'SadaPay';
      case PaymentMethodType.NAYAPAY:
        return 'NayaPay';
      default:
        return type;
    }
  };

  const handleSetDefault = (paymentMethodId: string) => {
    Alert.alert(
      'Set Default Payment Method',
      'Are you sure you want to set this as your default payment method?',
      [
        { text: 'Cancel', style: 'cancel' },
        {
          text: 'Confirm',
          onPress: () => {
            setPaymentMethods(prev => prev.map(pm => ({
              ...pm,
              isDefault: pm.id === paymentMethodId,
            })));
            Alert.alert('Success', 'Default payment method updated successfully!');
          },
        },
      ]
    );
  };

  const handleToggleActive = (paymentMethodId: string) => {
    const paymentMethod = paymentMethods.find(pm => pm.id === paymentMethodId);
    if (paymentMethod?.isDefault && paymentMethod.isActive) {
      Alert.alert('Error', 'Cannot deactivate default payment method. Please set another method as default first.');
      return;
    }

    setPaymentMethods(prev => prev.map(pm => 
      pm.id === paymentMethodId 
        ? { ...pm, isActive: !pm.isActive }
        : pm
    ));
    
    const newStatus = !paymentMethod?.isActive;
    Alert.alert('Success', `Payment method ${newStatus ? 'activated' : 'deactivated'} successfully!`);
  };

  const handleDeletePaymentMethod = (paymentMethodId: string) => {
    const paymentMethod = paymentMethods.find(pm => pm.id === paymentMethodId);
    if (paymentMethod?.isDefault) {
      Alert.alert('Error', 'Cannot delete default payment method. Please set another method as default first.');
      return;
    }

    Alert.alert(
      'Delete Payment Method',
      'Are you sure you want to delete this payment method? This action cannot be undone.',
      [
        { text: 'Cancel', style: 'cancel' },
        {
          text: 'Delete',
          style: 'destructive',
          onPress: () => {
            setPaymentMethods(prev => prev.filter(pm => pm.id !== paymentMethodId));
            Alert.alert('Success', 'Payment method deleted successfully!');
          },
        },
      ]
    );
  };

  const resetForm = () => {
    setFormData({
      type: PaymentMethodType.BANK_ACCOUNT,
      name: '',
      details: {},
    });
    setSelectedType(PaymentMethodType.BANK_ACCOUNT);
  };

  const handleAddPaymentMethod = async () => {
    // Validate required fields based on type
    if (!formData.name) {
      Alert.alert('Error', 'Please enter a name for this payment method.');
      return;
    }

    let isValid = true;
    let errorMessage = '';

    switch (selectedType) {
      case PaymentMethodType.BANK_ACCOUNT:
        if (!formData.details.bankName || !formData.details.accountTitle || !formData.details.accountNumber) {
          isValid = false;
          errorMessage = 'Please fill in all bank account details.';
        }
        break;
      case PaymentMethodType.JAZZCASH:
      case PaymentMethodType.EASYPAISA:
      case PaymentMethodType.SADAPAY:
      case PaymentMethodType.NAYAPAY:
        if (!formData.details.phoneNumber || !formData.details.accountTitle) {
          isValid = false;
          errorMessage = 'Please fill in all mobile wallet details.';
        }
        break;
    }

    if (!isValid) {
      Alert.alert('Error', errorMessage);
      return;
    }

    try {
      const newPaymentMethod: PaymentMethod = {
        id: Date.now().toString(),
        type: selectedType,
        name: formData.name,
        isDefault: paymentMethods.length === 0, // Set as default if it's the first payment method
        isActive: true,
        isVerified: false, // New payment methods need verification
        createdAt: new Date().toISOString(),
        updatedAt: new Date().toISOString(),
        details: formData.details,
      };

      setPaymentMethods(prev => [...prev, newPaymentMethod]);
      setShowAddModal(false);
      resetForm();
      Alert.alert('Success', 'Payment method added successfully! It will be verified within 24 hours.');
    } catch (error) {
      Alert.alert('Error', 'Failed to add payment method. Please try again.');
    }
  };

  const renderPaymentMethodCard = (paymentMethod: PaymentMethod) => (
    <Card key={paymentMethod.id} variant="elevated" margin="md" padding="lg">
      <View style={{ flexDirection: 'row', justifyContent: 'space-between', alignItems: 'flex-start', marginBottom: 16 }}>
        <View style={{ flex: 1 }}>
          <View style={{ flexDirection: 'row', alignItems: 'center', marginBottom: 8 }}>
            <View style={{
              width: 40,
              height: 40,
              backgroundColor: getPaymentMethodColor(paymentMethod.type),
              borderRadius: 20,
              alignItems: 'center',
              justifyContent: 'center',
              marginRight: 12,
            }}>
              <Ionicons name={getPaymentMethodIcon(paymentMethod.type) as any} size={20} color="white" />
            </View>
            
            <View style={{ flex: 1 }}>
              <Text style={{ fontSize: 16, fontWeight: 'bold', color: '#111827' }}>
                {paymentMethod.name}
              </Text>
              <Text style={{ fontSize: 12, color: '#6b7280' }}>
                {formatPaymentMethodName(paymentMethod.type)}
              </Text>
            </View>

            <View style={{ flexDirection: 'row', gap: 4 }}>
              {paymentMethod.isDefault && (
                <Badge text="Default" style={{ backgroundColor: '#10b981' }} />
              )}
              {paymentMethod.isVerified ? (
                <Badge text="Verified" style={{ backgroundColor: '#3b82f6' }} />
              ) : (
                <Badge text="Pending" style={{ backgroundColor: '#f59e0b' }} />
              )}
              {!paymentMethod.isActive && (
                <Badge text="Inactive" style={{ backgroundColor: '#6b7280' }} />
              )}
            </View>
          </View>

          {/* Payment Method Details */}
          <View style={{ gap: 4 }}>
            {paymentMethod.type === PaymentMethodType.BANK_ACCOUNT ? (
              <>
                <Text style={{ fontSize: 14, color: '#6b7280' }}>
                  <Text style={{ fontWeight: '600' }}>Bank:</Text> {paymentMethod.details.bankName}
                </Text>
                <Text style={{ fontSize: 14, color: '#6b7280' }}>
                  <Text style={{ fontWeight: '600' }}>Account:</Text> {paymentMethod.details.accountNumber}
                </Text>
                {paymentMethod.details.iban && (
                  <Text style={{ fontSize: 14, color: '#6b7280' }}>
                    <Text style={{ fontWeight: '600' }}>IBAN:</Text> {paymentMethod.details.iban}
                  </Text>
                )}
              </>
            ) : (
              <Text style={{ fontSize: 14, color: '#6b7280' }}>
                <Text style={{ fontWeight: '600' }}>Phone:</Text> {paymentMethod.details.phoneNumber}
              </Text>
            )}
            <Text style={{ fontSize: 14, color: '#6b7280' }}>
              <Text style={{ fontWeight: '600' }}>Title:</Text> {paymentMethod.details.accountTitle}
            </Text>
          </View>
        </View>
      </View>

      {/* Action Buttons */}
      <View style={{ flexDirection: 'row', gap: 8, borderTopWidth: 1, borderTopColor: '#e5e7eb', paddingTop: 12 }}>
        {!paymentMethod.isDefault && (
          <TouchableOpacity
            onPress={() => handleSetDefault(paymentMethod.id)}
            style={{
              flex: 1,
              backgroundColor: '#3b82f6',
              paddingVertical: 8,
              paddingHorizontal: 12,
              borderRadius: 6,
              alignItems: 'center',
            }}
          >
            <Text style={{ fontSize: 12, fontWeight: '600', color: 'white' }}>
              Set Default
            </Text>
          </TouchableOpacity>
        )}
        
        <TouchableOpacity
          onPress={() => handleToggleActive(paymentMethod.id)}
          style={{
            flex: 1,
            backgroundColor: paymentMethod.isActive ? '#f59e0b' : '#10b981',
            paddingVertical: 8,
            paddingHorizontal: 12,
            borderRadius: 6,
            alignItems: 'center',
          }}
        >
          <Text style={{ fontSize: 12, fontWeight: '600', color: 'white' }}>
            {paymentMethod.isActive ? 'Deactivate' : 'Activate'}
          </Text>
        </TouchableOpacity>
        
        <TouchableOpacity
          onPress={() => handleDeletePaymentMethod(paymentMethod.id)}
          style={{
            backgroundColor: '#ef4444',
            paddingVertical: 8,
            paddingHorizontal: 12,
            borderRadius: 6,
            alignItems: 'center',
          }}
        >
          <Ionicons name="trash" size={14} color="white" />
        </TouchableOpacity>
      </View>

      {!paymentMethod.isVerified && (
        <View style={{ marginTop: 12, padding: 12, backgroundColor: '#fef2f2', borderRadius: 8, borderLeftWidth: 4, borderLeftColor: '#ef4444' }}>
          <Text style={{ fontSize: 12, color: '#92400e', fontWeight: '600' }}>
            ⚠️ Verification Pending
          </Text>
          <Text style={{ fontSize: 12, color: '#92400e', marginTop: 2 }}>
            This payment method is under review. You'll be notified once verified.
          </Text>
        </View>
      )}
    </Card>
  );

  const renderAddPaymentMethodModal = () => (
    <Modal
      visible={showAddModal}
      animationType="slide"
      presentationStyle="pageSheet"
      onRequestClose={() => setShowAddModal(false)}
    >
      <SafeAreaView style={{ flex: 1, backgroundColor: '#f8fafc' }}>
        <View style={{
          paddingHorizontal: 20,
          paddingVertical: 16,
          backgroundColor: '#ffffff',
          borderBottomWidth: 1,
          borderBottomColor: '#e5e7eb',
          flexDirection: 'row',
          alignItems: 'center',
          justifyContent: 'space-between',
        }}>
          <TouchableOpacity onPress={() => { setShowAddModal(false); resetForm(); }}>
            <Ionicons name="close" size={24} color="#6b7280" />
          </TouchableOpacity>
          <Text style={{ fontSize: 18, fontWeight: 'bold', color: '#111827' }}>
            Add Payment Method
          </Text>
          <View style={{ width: 24 }} />
        </View>

        <ScrollView style={{ flex: 1 }} contentContainerStyle={{ padding: 20 }}>
          <Card variant="elevated" padding="lg">
            <Text style={{ fontSize: 16, fontWeight: 'bold', color: '#111827', marginBottom: 16 }}>
              Payment Method Type
            </Text>

            {/* Payment Method Type Selection */}
            <View style={{ gap: 8, marginBottom: 24 }}>
              {Object.values(PaymentMethodType).map((type) => (
                <TouchableOpacity
                  key={type}
                  onPress={() => {
                    setSelectedType(type);
                    setFormData(prev => ({ ...prev, type, details: {} }));
                  }}
                  style={{
                    flexDirection: 'row',
                    alignItems: 'center',
                    backgroundColor: selectedType === type ? '#fef2f2' : '#ffffff',
                    borderColor: selectedType === type ? '#ef4444' : '#e5e7eb',
                    borderWidth: 1,
                    paddingVertical: 16,
                    paddingHorizontal: 16,
                    borderRadius: 8,
                  }}
                >
                  <View style={{
                    width: 40,
                    height: 40,
                    backgroundColor: selectedType === type ? getPaymentMethodColor(type) : '#f3f4f6',
                    borderRadius: 20,
                    alignItems: 'center',
                    justifyContent: 'center',
                    marginRight: 12,
                  }}>
                    <Ionicons
                      name={getPaymentMethodIcon(type) as any}
                      size={20}
                      color={selectedType === type ? 'white' : '#6b7280'}
                    />
                  </View>

                  <Text style={{
                    fontSize: 16,
                    fontWeight: '600',
                    color: selectedType === type ? '#ef4444' : '#111827',
                    flex: 1,
                  }}>
                    {formatPaymentMethodName(type)}
                  </Text>

                  {selectedType === type && (
                    <Ionicons name="checkmark-circle" size={20} color="#ef4444" />
                  )}
                </TouchableOpacity>
              ))}
            </View>

            {/* Payment Method Name */}
            <View style={{ marginBottom: 16 }}>
              <Text style={{ fontSize: 14, fontWeight: '600', color: '#374151', marginBottom: 8 }}>
                Payment Method Name *
              </Text>
              <TextInput
                value={formData.name}
                onChangeText={(text) => setFormData(prev => ({ ...prev, name: text }))}
                placeholder={`My ${formatPaymentMethodName(selectedType)}`}
                style={{
                  backgroundColor: '#ffffff',
                  borderColor: '#e5e7eb',
                  borderWidth: 1,
                  paddingVertical: 12,
                  paddingHorizontal: 16,
                  borderRadius: 8,
                  fontSize: 16,
                  color: '#111827',
                }}
              />
            </View>

            {/* Dynamic Form Fields Based on Type */}
            {selectedType === PaymentMethodType.BANK_ACCOUNT ? (
              <>
                <View style={{ marginBottom: 16 }}>
                  <Text style={{ fontSize: 14, fontWeight: '600', color: '#374151', marginBottom: 8 }}>
                    Bank Name *
                  </Text>
                  <TextInput
                    value={formData.details.bankName || ''}
                    onChangeText={(text) => setFormData(prev => ({
                      ...prev,
                      details: { ...prev.details, bankName: text }
                    }))}
                    placeholder="e.g., Habib Bank Limited"
                    style={{
                      backgroundColor: '#ffffff',
                      borderColor: '#e5e7eb',
                      borderWidth: 1,
                      paddingVertical: 12,
                      paddingHorizontal: 16,
                      borderRadius: 8,
                      fontSize: 16,
                      color: '#111827',
                    }}
                  />
                </View>

                <View style={{ marginBottom: 16 }}>
                  <Text style={{ fontSize: 14, fontWeight: '600', color: '#374151', marginBottom: 8 }}>
                    Account Title *
                  </Text>
                  <TextInput
                    value={formData.details.accountTitle || ''}
                    onChangeText={(text) => setFormData(prev => ({
                      ...prev,
                      details: { ...prev.details, accountTitle: text }
                    }))}
                    placeholder="Account holder name"
                    style={{
                      backgroundColor: '#ffffff',
                      borderColor: '#e5e7eb',
                      borderWidth: 1,
                      paddingVertical: 12,
                      paddingHorizontal: 16,
                      borderRadius: 8,
                      fontSize: 16,
                      color: '#111827',
                    }}
                  />
                </View>

                <View style={{ marginBottom: 16 }}>
                  <Text style={{ fontSize: 14, fontWeight: '600', color: '#374151', marginBottom: 8 }}>
                    Account Number *
                  </Text>
                  <TextInput
                    value={formData.details.accountNumber || ''}
                    onChangeText={(text) => setFormData(prev => ({
                      ...prev,
                      details: { ...prev.details, accountNumber: text }
                    }))}
                    placeholder="Account number"
                    keyboardType="numeric"
                    style={{
                      backgroundColor: '#ffffff',
                      borderColor: '#e5e7eb',
                      borderWidth: 1,
                      paddingVertical: 12,
                      paddingHorizontal: 16,
                      borderRadius: 8,
                      fontSize: 16,
                      color: '#111827',
                    }}
                  />
                </View>

                <View style={{ marginBottom: 24 }}>
                  <Text style={{ fontSize: 14, fontWeight: '600', color: '#374151', marginBottom: 8 }}>
                    IBAN (Optional)
                  </Text>
                  <TextInput
                    value={formData.details.iban || ''}
                    onChangeText={(text) => setFormData(prev => ({
                      ...prev,
                      details: { ...prev.details, iban: text.toUpperCase() }
                    }))}
                    placeholder="PK36HABB00***********234"
                    autoCapitalize="characters"
                    style={{
                      backgroundColor: '#ffffff',
                      borderColor: '#e5e7eb',
                      borderWidth: 1,
                      paddingVertical: 12,
                      paddingHorizontal: 16,
                      borderRadius: 8,
                      fontSize: 16,
                      color: '#111827',
                    }}
                  />
                </View>
              </>
            ) : (
              <>
                <View style={{ marginBottom: 16 }}>
                  <Text style={{ fontSize: 14, fontWeight: '600', color: '#374151', marginBottom: 8 }}>
                    Phone Number *
                  </Text>
                  <TextInput
                    value={formData.details.phoneNumber || ''}
                    onChangeText={(text) => setFormData(prev => ({
                      ...prev,
                      details: { ...prev.details, phoneNumber: text }
                    }))}
                    placeholder="+92 300 1234567"
                    keyboardType="phone-pad"
                    style={{
                      backgroundColor: '#ffffff',
                      borderColor: '#e5e7eb',
                      borderWidth: 1,
                      paddingVertical: 12,
                      paddingHorizontal: 16,
                      borderRadius: 8,
                      fontSize: 16,
                      color: '#111827',
                    }}
                  />
                </View>

                <View style={{ marginBottom: 24 }}>
                  <Text style={{ fontSize: 14, fontWeight: '600', color: '#374151', marginBottom: 8 }}>
                    Account Title *
                  </Text>
                  <TextInput
                    value={formData.details.accountTitle || ''}
                    onChangeText={(text) => setFormData(prev => ({
                      ...prev,
                      details: { ...prev.details, accountTitle: text }
                    }))}
                    placeholder="Account holder name"
                    style={{
                      backgroundColor: '#ffffff',
                      borderColor: '#e5e7eb',
                      borderWidth: 1,
                      paddingVertical: 12,
                      paddingHorizontal: 16,
                      borderRadius: 8,
                      fontSize: 16,
                      color: '#111827',
                    }}
                  />
                </View>
              </>
            )}

            <Button
              title="Add Payment Method"
              onPress={handleAddPaymentMethod}
              style={{ backgroundColor: '#ef4444' }}
            />
          </Card>
        </ScrollView>
      </SafeAreaView>
    </Modal>
  );

  return (
    <SafeAreaView style={{ flex: 1, backgroundColor: '#f8fafc' }}>
      <View style={{
        paddingHorizontal: 20,
        paddingVertical: 16,
        backgroundColor: '#ffffff',
        borderBottomWidth: 1,
        borderBottomColor: '#e5e7eb',
        flexDirection: 'row',
        alignItems: 'center',
        justifyContent: 'space-between',
      }}>
        <View style={{ flexDirection: 'row', alignItems: 'center' }}>
          <TouchableOpacity
            onPress={() => navigation.goBack()}
            style={{ marginRight: 16 }}
          >
            <Ionicons name="arrow-back" size={24} color="#111827" />
          </TouchableOpacity>
          <Text style={{ fontSize: 20, fontWeight: 'bold', color: '#111827' }}>
            Payment Methods
          </Text>
        </View>

        <TouchableOpacity
          onPress={() => setShowAddModal(true)}
          style={{
            backgroundColor: '#ef4444',
            paddingHorizontal: 12,
            paddingVertical: 8,
            borderRadius: 8,
            flexDirection: 'row',
            alignItems: 'center',
          }}
        >
          <Ionicons name="add" size={16} color="white" />
          <Text style={{ fontSize: 14, fontWeight: '600', color: 'white', marginLeft: 4 }}>
            Add
          </Text>
        </TouchableOpacity>
      </View>

      <ScrollView style={{ flex: 1 }} showsVerticalScrollIndicator={false}>
        {paymentMethods.length === 0 ? (
          <View style={{ flex: 1, alignItems: 'center', justifyContent: 'center', padding: 40 }}>
            <Ionicons name="card-outline" size={64} color="#d1d5db" />
            <Text style={{ fontSize: 18, fontWeight: 'bold', color: '#6b7280', marginTop: 16, textAlign: 'center' }}>
              No Payment Methods
            </Text>
            <Text style={{ fontSize: 14, color: '#9ca3af', marginTop: 8, textAlign: 'center' }}>
              Add a payment method to receive your earnings
            </Text>
            <Button
              title="Add Payment Method"
              onPress={() => setShowAddModal(true)}
              style={{ backgroundColor: '#ef4444', marginTop: 24 }}
            />
          </View>
        ) : (
          <>
            {paymentMethods.map(renderPaymentMethodCard)}

            {/* Bottom spacing */}
            <View style={{ height: 20 }} />
          </>
        )}
      </ScrollView>

      {renderAddPaymentMethodModal()}
    </SafeAreaView>
  );
};

export default PaymentMethodsScreen;
