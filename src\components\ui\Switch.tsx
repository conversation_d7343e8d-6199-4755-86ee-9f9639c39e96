import React from 'react';
import { View, Text, Switch as RNSwitch, ViewStyle, TextStyle } from 'react-native';

interface SwitchProps {
  value: boolean;
  onValueChange: (value: boolean) => void;
  label?: string;
  description?: string;
  disabled?: boolean;
  size?: 'sm' | 'md' | 'lg';
  style?: ViewStyle;
}

const Switch: React.FC<SwitchProps> = ({
  value,
  onValueChange,
  label,
  description,
  disabled = false,
  size = 'md',
  style,
}) => {
  const getContainerStyles = (): ViewStyle => {
    return {
      flexDirection: 'row',
      alignItems: 'center',
      justifyContent: 'space-between',
      paddingVertical: 8,
    };
  };

  const getContentStyles = (): ViewStyle => {
    return {
      flex: 1,
      marginRight: 16,
    };
  };

  const getLabelStyles = (): TextStyle => {
    const sizeStyles: Record<string, TextStyle> = {
      sm: { fontSize: 14 },
      md: { fontSize: 16 },
      lg: { fontSize: 18 },
    };

    return {
      fontWeight: '500',
      color: disabled ? '#9ca3af' : '#111827',
      ...sizeStyles[size],
    };
  };

  const getDescriptionStyles = (): TextStyle => {
    return {
      fontSize: 14,
      color: disabled ? '#d1d5db' : '#6b7280',
      marginTop: 2,
    };
  };

  const getSwitchScale = (): number => {
    const scales = { sm: 0.8, md: 1, lg: 1.2 };
    return scales[size];
  };

  return (
    <View style={[getContainerStyles(), style]}>
      {(label || description) && (
        <View style={getContentStyles()}>
          {label && <Text style={getLabelStyles()}>{label}</Text>}
          {description && <Text style={getDescriptionStyles()}>{description}</Text>}
        </View>
      )}
      
      <RNSwitch
        value={value}
        onValueChange={onValueChange}
        disabled={disabled}
        trackColor={{
          false: '#d1d5db',
          true: '#fed7aa',
        }}
        thumbColor={value ? '#f97316' : '#ffffff'}
        ios_backgroundColor="#d1d5db"
        style={{
          transform: [{ scaleX: getSwitchScale() }, { scaleY: getSwitchScale() }],
        }}
      />
    </View>
  );
};

export default Switch;
