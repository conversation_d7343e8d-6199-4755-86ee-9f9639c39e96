import React, { useState, useEffect, useRef } from 'react';
import {
  View,
  Text,
  ScrollView,
  TouchableOpacity,
  Image,
  Share,
  Alert,
  Animated,
  StatusBar,
  RefreshControl,
  Clipboard,
  ToastAndroid,
  Platform,
} from 'react-native';
import { SafeAreaView } from 'react-native-safe-area-context';
import { Ionicons } from '@expo/vector-icons';
import { useNavigation } from '@react-navigation/native';
import { LinearGradient } from 'expo-linear-gradient';
import { useProfile } from '../../context/ProfileContext';
import { useAuth } from '../../context/AuthContext';
import { DocumentStatus, VehicleType, PaymentMethodType } from '../../types/profile';

const ProfileMainScreen: React.FC = () => {
  const navigation = useNavigation();
  const { state, loadProfile } = useProfile();
  const { logout } = useAuth();
  const [refreshing, setRefreshing] = useState(false);

  // Animation refs
  const fadeAnimation = useRef(new Animated.Value(0)).current;
  const slideAnimation = useRef(new Animated.Value(50)).current;
  const scaleAnimation = useRef(new Animated.Value(0.9)).current;

  // Mock data (in real app, this would come from context)
  const mockProfile = {
    id: '1',
    personalInfo: {
      firstName: 'Muhammad',
      lastName: 'Ahmed',
      email: '<EMAIL>',
      phone: '+92 300 1234567',
      cnic: '35202-1234567-1',
      profilePicture: 'https://images.unsplash.com/photo-1507003211169-0a1dd7228f2d?w=150&h=150&fit=crop&crop=face',
    },
    riderInfo: {
      riderCode: 'FW-RD-2024-001',
      rating: 4.8,
      totalDeliveries: 1247,
      status: 'active' as const,
    },
    documents: [
      { id: '1', type: 'cnic', name: 'CNIC', status: DocumentStatus.VERIFIED },
      { id: '2', type: 'license', name: 'Driving License', status: DocumentStatus.VERIFIED },
      { id: '3', type: 'vehicle_registration', name: 'Vehicle Registration', status: DocumentStatus.PENDING },
      { id: '4', type: 'insurance', name: 'Vehicle Insurance', status: DocumentStatus.NOT_UPLOADED },
    ],
    vehicles: [{
      id: '1',
      type: VehicleType.MOTORCYCLE,
      make: 'Honda',
      model: 'CD 70',
      plateNumber: 'LES-1234',
      isActive: true,
    }],
    paymentMethods: [{
      id: '1',
      type: PaymentMethodType.BANK_ACCOUNT,
      name: 'HBL Account',
      isDefault: true,
      details: {
        bankName: 'Habib Bank Limited',
        accountTitle: 'Muhammad Ahmed',
        iban: '************************',
      },
    }],
  };

  useEffect(() => {
    loadData();
    
    // Start entrance animations
    Animated.parallel([
      Animated.timing(fadeAnimation, {
        toValue: 1,
        duration: 800,
        useNativeDriver: true,
      }),
      Animated.timing(slideAnimation, {
        toValue: 0,
        duration: 800,
        useNativeDriver: true,
      }),
      Animated.timing(scaleAnimation, {
        toValue: 1,
        duration: 800,
        useNativeDriver: true,
      }),
    ]).start();
  }, []);

  const loadData = async () => {
    try {
      await loadProfile();
    } catch (error) {
      console.error('Failed to load profile:', error);
    }
  };

  const onRefresh = async () => {
    setRefreshing(true);
    await loadData();
    setRefreshing(false);
  };

  const handleShareReferralCode = async () => {
    try {
      await Share.share({
        message: `🚀 Join FoodWay as a delivery rider using my referral code: ${mockProfile.riderInfo.riderCode}\n\n💰 Start earning up to PKR 50,000/month\n🎁 Get PKR 500 bonus on your first delivery\n📱 Download the FoodWay Rider app now!\n\n#FoodWayRider #DeliveryJobs #EarnMoney`,
        title: 'Join FoodWay Delivery - Earn Big!',
      });
    } catch (error) {
      console.error('Error sharing referral code:', error);
    }
  };

  const handleCopyReferralCode = async () => {
    try {
      await Clipboard.setString(mockProfile.riderInfo.riderCode);

      // Show rich notification
      if (Platform.OS === 'android') {
        ToastAndroid.showWithGravityAndOffset(
          '✅ Referral code copied to clipboard!',
          ToastAndroid.LONG,
          ToastAndroid.BOTTOM,
          25,
          50
        );
      } else {
        Alert.alert('✅ Copied!', 'Referral code copied to clipboard');
      }
    } catch (error) {
      console.error('Error copying referral code:', error);
      Alert.alert('Error', 'Failed to copy referral code');
    }
  };

  const handleLogout = () => {
    Alert.alert(
      'Logout',
      'Are you sure you want to logout?',
      [
        { text: 'Cancel', style: 'cancel' },
        {
          text: 'Logout',
          style: 'destructive',
          onPress: async () => {
            try {
              await logout();
            } catch (error: any) {
              Alert.alert('Error', error.message || 'Failed to logout');
            }
          }
        },
      ]
    );
  };

  const getDocumentStatusIcon = (status: DocumentStatus) => {
    switch (status) {
      case DocumentStatus.VERIFIED:
        return { name: 'checkmark-circle', color: '#10b981' };
      case DocumentStatus.PENDING:
        return { name: 'time', color: '#f59e0b' };
      case DocumentStatus.REJECTED:
        return { name: 'close-circle', color: '#ef4444' };
      case DocumentStatus.NOT_UPLOADED:
        return { name: 'cloud-upload', color: '#6b7280' };
      default:
        return { name: 'help-circle', color: '#6b7280' };
    }
  };

  const getVehicleIcon = (type: VehicleType) => {
    switch (type) {
      case VehicleType.MOTORCYCLE:
        return 'bicycle-outline';
      case VehicleType.CAR:
        return 'car-outline';
      case VehicleType.BICYCLE:
        return 'bicycle-outline';
      case VehicleType.SCOOTER:
        return 'bicycle-outline';
      default:
        return 'bicycle-outline';
    }
  };

  const getPaymentMethodIcon = (type: PaymentMethodType) => {
    switch (type) {
      case PaymentMethodType.BANK_ACCOUNT:
        return 'card';
      case PaymentMethodType.JAZZCASH:
        return 'phone-portrait';
      case PaymentMethodType.EASYPAISA:
        return 'phone-portrait';
      default:
        return 'card';
    }
  };

  const renderHeader = () => (
    <LinearGradient
      colors={['#f97316', '#ea580c']}
      style={{
        paddingTop: StatusBar.currentHeight || 0,
        paddingBottom: 20,
      }}
    >
      <SafeAreaView>
        <View style={{
          flexDirection: 'row',
          alignItems: 'center',
          justifyContent: 'space-between',
          paddingHorizontal: 20,
          paddingTop: 16,
        }}>
          <Text style={{
            fontSize: 24,
            fontWeight: 'bold',
            color: 'white',
          }}>
            Profile
          </Text>

          <TouchableOpacity
            onPress={() => navigation.navigate('Settings' as never)}
            style={{
              width: 40,
              height: 40,
              borderRadius: 20,
              backgroundColor: 'rgba(255,255,255,0.2)',
              alignItems: 'center',
              justifyContent: 'center',
            }}
          >
            <Ionicons name="settings" size={20} color="white" />
          </TouchableOpacity>
        </View>
      </SafeAreaView>
    </LinearGradient>
  );

  const renderProfileHeader = () => (
    <Animated.View
      style={{
        opacity: fadeAnimation,
        transform: [
          { translateY: slideAnimation },
          { scale: scaleAnimation },
        ],
      }}
    >
      <View style={{
        backgroundColor: 'white',
        marginHorizontal: 20,
        marginTop: -30,
        borderRadius: 20,
        padding: 24,
        alignItems: 'center',
        shadowColor: '#000',
        shadowOffset: { width: 0, height: 4 },
        shadowOpacity: 0.15,
        shadowRadius: 12,
        elevation: 8,
      }}>
        {/* Avatar */}
        <TouchableOpacity
          style={{
            position: 'relative',
            marginBottom: 16,
          }}
        >
          <Image
            source={{
              uri: mockProfile.personalInfo.profilePicture || 'https://via.placeholder.com/120x120/e5e7eb/6b7280?text=No+Image'
            }}
            style={{
              width: 100,
              height: 100,
              borderRadius: 50,
              backgroundColor: '#f3f4f6',
              borderWidth: 4,
              borderColor: '#f97316',
            }}
          />
          <View style={{
            position: 'absolute',
            bottom: 0,
            right: 0,
            backgroundColor: '#f97316',
            borderRadius: 15,
            width: 30,
            height: 30,
            alignItems: 'center',
            justifyContent: 'center',
            borderWidth: 3,
            borderColor: 'white',
          }}>
            <Ionicons name="camera" size={14} color="white" />
          </View>
        </TouchableOpacity>

        {/* Name and Rating */}
        <Text style={{
          fontSize: 24,
          fontWeight: 'bold',
          color: '#111827',
          marginBottom: 4,
        }}>
          {mockProfile.personalInfo.firstName} {mockProfile.personalInfo.lastName}
        </Text>

        <View style={{
          flexDirection: 'row',
          alignItems: 'center',
          backgroundColor: '#fef3c7',
          paddingHorizontal: 12,
          paddingVertical: 6,
          borderRadius: 20,
          marginBottom: 16,
        }}>
          <Ionicons name="star" size={16} color="#f59e0b" />
          <Text style={{
            fontSize: 16,
            fontWeight: '600',
            color: '#92400e',
            marginLeft: 4,
          }}>
            {mockProfile.riderInfo.rating}
          </Text>
          <Text style={{
            fontSize: 14,
            color: '#92400e',
            marginLeft: 8,
          }}>
            ({mockProfile.riderInfo.totalDeliveries} deliveries)
          </Text>
        </View>

        {/* Rider Code */}
        <View style={{
          backgroundColor: '#f8fafc',
          borderRadius: 12,
          padding: 12,
          width: '100%',
          alignItems: 'center',
        }}>
          <Text style={{
            fontSize: 12,
            color: '#6b7280',
            marginBottom: 4,
          }}>
            Rider ID
          </Text>
          <Text style={{
            fontSize: 16,
            fontWeight: '600',
            color: '#111827',
            letterSpacing: 1,
          }}>
            {mockProfile.riderInfo.riderCode}
          </Text>
        </View>
      </View>
    </Animated.View>
  );

  return (
    <View style={{ flex: 1, backgroundColor: '#f8fafc' }}>
      <StatusBar barStyle="light-content" backgroundColor="#f97316" />
      
      {renderHeader()}

      <ScrollView
        style={{ flex: 1 }}
        refreshControl={
          <RefreshControl refreshing={refreshing} onRefresh={onRefresh} />
        }
        showsVerticalScrollIndicator={false}
        contentContainerStyle={{ paddingBottom: 100 }}
      >
        {renderProfileHeader()}

        {/* Personal Info Section */}
        {renderPersonalInfoSection()}

        {/* Vehicle Info Section */}
        {renderVehicleInfoSection()}

        {/* Bank Info Section */}
        {renderBankInfoSection()}

        {/* Document Status Section */}
        {renderDocumentStatusSection()}

        {/* Referral Section */}
        {renderReferralSection()}

        {/* Action Buttons */}
        {renderActionButtons()}
      </ScrollView>
    </View>
  );

  function renderPersonalInfoSection() {
    return (
      <Animated.View
        style={{
          opacity: fadeAnimation,
          transform: [{ translateY: slideAnimation }],
        }}
      >
        <View style={{
          backgroundColor: 'white',
          marginHorizontal: 20,
          marginTop: 20,
          borderRadius: 16,
          padding: 20,
          shadowColor: '#000',
          shadowOffset: { width: 0, height: 2 },
          shadowOpacity: 0.1,
          shadowRadius: 8,
          elevation: 3,
        }}>
          <View style={{
            flexDirection: 'row',
            alignItems: 'center',
            justifyContent: 'space-between',
            marginBottom: 16,
          }}>
            <View style={{
              flexDirection: 'row',
              alignItems: 'center',
            }}>
              <Ionicons name="person" size={20} color="#f97316" />
              <Text style={{
                fontSize: 18,
                fontWeight: 'bold',
                color: '#111827',
                marginLeft: 8,
              }}>
                Personal Information
              </Text>
            </View>

            <TouchableOpacity
              onPress={() => navigation.navigate('EditProfile' as never)}
              style={{
                flexDirection: 'row',
                alignItems: 'center',
                backgroundColor: '#f97316',
                paddingHorizontal: 12,
                paddingVertical: 6,
                borderRadius: 8,
              }}
            >
              <Ionicons name="pencil" size={14} color="white" />
              <Text style={{
                fontSize: 12,
                fontWeight: '600',
                color: 'white',
                marginLeft: 4,
              }}>
                Edit
              </Text>
            </TouchableOpacity>
          </View>

          <View style={{ gap: 12 }}>
            <View style={{
              flexDirection: 'row',
              alignItems: 'center',
              paddingVertical: 8,
            }}>
              <Ionicons name="mail" size={16} color="#6b7280" />
              <View style={{ marginLeft: 12, flex: 1 }}>
                <Text style={{
                  fontSize: 12,
                  color: '#6b7280',
                  marginBottom: 2,
                }}>
                  Email
                </Text>
                <Text style={{
                  fontSize: 14,
                  color: '#111827',
                  fontWeight: '500',
                }}>
                  {mockProfile.personalInfo.email}
                </Text>
              </View>
            </View>

            <View style={{
              flexDirection: 'row',
              alignItems: 'center',
              paddingVertical: 8,
            }}>
              <Ionicons name="call" size={16} color="#6b7280" />
              <View style={{ marginLeft: 12, flex: 1 }}>
                <Text style={{
                  fontSize: 12,
                  color: '#6b7280',
                  marginBottom: 2,
                }}>
                  Phone
                </Text>
                <Text style={{
                  fontSize: 14,
                  color: '#111827',
                  fontWeight: '500',
                }}>
                  {mockProfile.personalInfo.phone}
                </Text>
              </View>
            </View>

            <View style={{
              flexDirection: 'row',
              alignItems: 'center',
              paddingVertical: 8,
            }}>
              <Ionicons name="card" size={16} color="#6b7280" />
              <View style={{ marginLeft: 12, flex: 1 }}>
                <Text style={{
                  fontSize: 12,
                  color: '#6b7280',
                  marginBottom: 2,
                }}>
                  CNIC
                </Text>
                <Text style={{
                  fontSize: 14,
                  color: '#111827',
                  fontWeight: '500',
                }}>
                  {mockProfile.personalInfo.cnic}
                </Text>
              </View>
            </View>
          </View>
        </View>
      </Animated.View>
    );
  }

  function renderVehicleInfoSection() {
    const vehicle = mockProfile.vehicles[0];

    return (
      <Animated.View
        style={{
          opacity: fadeAnimation,
          transform: [{ translateY: slideAnimation }],
        }}
      >
        <View style={{
          backgroundColor: 'white',
          marginHorizontal: 20,
          marginTop: 16,
          borderRadius: 16,
          padding: 20,
          shadowColor: '#000',
          shadowOffset: { width: 0, height: 2 },
          shadowOpacity: 0.1,
          shadowRadius: 8,
          elevation: 3,
        }}>
          <View style={{
            flexDirection: 'row',
            alignItems: 'center',
            justifyContent: 'space-between',
            marginBottom: 16,
          }}>
            <View style={{
              flexDirection: 'row',
              alignItems: 'center',
            }}>
              <Ionicons name={getVehicleIcon(vehicle.type) as any} size={20} color="#f97316" />
              <Text style={{
                fontSize: 18,
                fontWeight: 'bold',
                color: '#111827',
                marginLeft: 8,
              }}>
                Vehicle Information
              </Text>
            </View>

            <TouchableOpacity
              onPress={() => navigation.navigate('VehicleInfo' as never)}
              style={{
                flexDirection: 'row',
                alignItems: 'center',
                backgroundColor: '#f97316',
                paddingHorizontal: 12,
                paddingVertical: 6,
                borderRadius: 8,
              }}
            >
              <Ionicons name="pencil" size={14} color="white" />
              <Text style={{
                fontSize: 12,
                fontWeight: '600',
                color: 'white',
                marginLeft: 4,
              }}>
                Edit
              </Text>
            </TouchableOpacity>
          </View>

          <View style={{
            flexDirection: 'row',
            alignItems: 'center',
            backgroundColor: '#f8fafc',
            borderRadius: 12,
            padding: 16,
          }}>
            <View style={{
              backgroundColor: '#f97316',
              borderRadius: 30,
              width: 60,
              height: 60,
              alignItems: 'center',
              justifyContent: 'center',
            }}>
              <Ionicons name={getVehicleIcon(vehicle.type) as any} size={24} color="white" />
            </View>

            <View style={{ marginLeft: 16, flex: 1 }}>
              <Text style={{
                fontSize: 16,
                fontWeight: 'bold',
                color: '#111827',
                marginBottom: 4,
              }}>
                {vehicle.make} {vehicle.model}
              </Text>
              <Text style={{
                fontSize: 14,
                color: '#6b7280',
                marginBottom: 2,
              }}>
                Plate: {vehicle.plateNumber}
              </Text>
              <View style={{
                flexDirection: 'row',
                alignItems: 'center',
              }}>
                <View style={{
                  width: 8,
                  height: 8,
                  borderRadius: 4,
                  backgroundColor: vehicle.isActive ? '#10b981' : '#ef4444',
                  marginRight: 6,
                }} />
                <Text style={{
                  fontSize: 12,
                  color: vehicle.isActive ? '#10b981' : '#ef4444',
                  fontWeight: '600',
                }}>
                  {vehicle.isActive ? 'Active' : 'Inactive'}
                </Text>
              </View>
            </View>
          </View>
        </View>
      </Animated.View>
    );
  }

  function renderBankInfoSection() {
    const paymentMethod = mockProfile.paymentMethods[0];

    // Get payment method display name
    const getPaymentMethodDisplayName = (type: string) => {
      switch (type) {
        case 'BANK_ACCOUNT': return 'Bank Information';
        case 'JAZZCASH': return 'JazzCash Wallet';
        case 'EASYPAISA': return 'EasyPaisa Wallet';
        case 'SADAPAY': return 'SadaPay Wallet';
        case 'NAYAPAY': return 'NayaPay Wallet';
        default: return 'Payment Method';
      }
    };

    return (
      <Animated.View
        style={{
          opacity: fadeAnimation,
          transform: [{ translateY: slideAnimation }],
        }}
      >
        <View style={{
          backgroundColor: 'white',
          marginHorizontal: 20,
          marginTop: 16,
          borderRadius: 16,
          padding: 20,
          shadowColor: '#000',
          shadowOffset: { width: 0, height: 2 },
          shadowOpacity: 0.1,
          shadowRadius: 8,
          elevation: 3,
        }}>
          <View style={{
            flexDirection: 'row',
            alignItems: 'center',
            justifyContent: 'space-between',
            marginBottom: 16,
          }}>
            <View style={{
              flexDirection: 'row',
              alignItems: 'center',
            }}>
              <Ionicons name={getPaymentMethodIcon(paymentMethod.type) as any} size={20} color="#f97316" />
              <Text style={{
                fontSize: 18,
                fontWeight: 'bold',
                color: '#111827',
                marginLeft: 8,
              }}>
                {getPaymentMethodDisplayName(paymentMethod.type)}
              </Text>
            </View>

            <TouchableOpacity
              onPress={() => navigation.navigate('BankInfo' as never)}
              style={{
                flexDirection: 'row',
                alignItems: 'center',
                backgroundColor: '#f97316',
                paddingHorizontal: 12,
                paddingVertical: 6,
                borderRadius: 8,
              }}
            >
              <Ionicons name="pencil" size={14} color="white" />
              <Text style={{
                fontSize: 12,
                fontWeight: '600',
                color: 'white',
                marginLeft: 4,
              }}>
                Edit
              </Text>
            </TouchableOpacity>
          </View>

          <View style={{
            backgroundColor: '#f8fafc',
            borderRadius: 12,
            padding: 16,
          }}>
            <View style={{
              flexDirection: 'row',
              alignItems: 'center',
              marginBottom: 12,
            }}>
              <View style={{
                backgroundColor: '#10b981',
                borderRadius: 20,
                width: 40,
                height: 40,
                alignItems: 'center',
                justifyContent: 'center',
              }}>
                <Ionicons name="card" size={20} color="white" />
              </View>

              <View style={{ marginLeft: 12, flex: 1 }}>
                <Text style={{
                  fontSize: 16,
                  fontWeight: 'bold',
                  color: '#111827',
                  marginBottom: 2,
                }}>
                  {paymentMethod.details.bankName}
                </Text>
                <Text style={{
                  fontSize: 14,
                  color: '#6b7280',
                }}>
                  {paymentMethod.details.accountTitle}
                </Text>
              </View>

              {paymentMethod.isDefault && (
                <View style={{
                  backgroundColor: '#fef3c7',
                  paddingHorizontal: 8,
                  paddingVertical: 4,
                  borderRadius: 12,
                }}>
                  <Text style={{
                    fontSize: 10,
                    fontWeight: '600',
                    color: '#92400e',
                  }}>
                    DEFAULT
                  </Text>
                </View>
              )}
            </View>

            <View style={{
              borderTopWidth: 1,
              borderTopColor: '#e5e7eb',
              paddingTop: 12,
            }}>
              <Text style={{
                fontSize: 12,
                color: '#6b7280',
                marginBottom: 4,
              }}>
                IBAN
              </Text>
              <Text style={{
                fontSize: 14,
                color: '#111827',
                fontWeight: '500',
                fontFamily: 'monospace',
              }}>
                {paymentMethod.details.iban}
              </Text>
            </View>
          </View>
        </View>
      </Animated.View>
    );
  }

  function renderDocumentStatusSection() {
    return (
      <Animated.View
        style={{
          opacity: fadeAnimation,
          transform: [{ translateY: slideAnimation }],
        }}
      >
        <View style={{
          backgroundColor: 'white',
          marginHorizontal: 20,
          marginTop: 16,
          borderRadius: 16,
          padding: 20,
          shadowColor: '#000',
          shadowOffset: { width: 0, height: 2 },
          shadowOpacity: 0.1,
          shadowRadius: 8,
          elevation: 3,
        }}>
          <View style={{
            flexDirection: 'row',
            alignItems: 'center',
            marginBottom: 16,
          }}>
            <Ionicons name="document-text" size={20} color="#f97316" />
            <Text style={{
              fontSize: 18,
              fontWeight: 'bold',
              color: '#111827',
              marginLeft: 8,
            }}>
              Document Status
            </Text>
          </View>

          <View style={{ gap: 12 }}>
            {mockProfile.documents.map((document, index) => {
              const statusIcon = getDocumentStatusIcon(document.status);

              return (
                <TouchableOpacity
                  key={document.id}
                  onPress={() => navigation.navigate('DocumentStatus' as never)}
                  style={{
                    flexDirection: 'row',
                    alignItems: 'center',
                    paddingVertical: 12,
                    paddingHorizontal: 16,
                    backgroundColor: '#f8fafc',
                    borderRadius: 12,
                    borderLeftWidth: 4,
                    borderLeftColor: statusIcon.color,
                  }}
                >
                  <Ionicons
                    name={statusIcon.name as any}
                    size={20}
                    color={statusIcon.color}
                  />

                  <View style={{ marginLeft: 12, flex: 1 }}>
                    <Text style={{
                      fontSize: 14,
                      fontWeight: '600',
                      color: '#111827',
                      marginBottom: 2,
                    }}>
                      {document.name}
                    </Text>
                    <Text style={{
                      fontSize: 12,
                      color: statusIcon.color,
                      textTransform: 'capitalize',
                    }}>
                      {document.status.replace('_', ' ')}
                    </Text>
                  </View>

                  <Ionicons name="chevron-forward" size={16} color="#6b7280" />
                </TouchableOpacity>
              );
            })}

            {/* View All Documents Button */}
            <TouchableOpacity
              onPress={() => navigation.navigate('DocumentStatus' as never)}
              style={{
                flexDirection: 'row',
                alignItems: 'center',
                justifyContent: 'center',
                backgroundColor: '#f97316',
                paddingVertical: 12,
                paddingHorizontal: 16,
                borderRadius: 12,
                marginTop: 8,
              }}
            >
              <Ionicons name="document-text" size={16} color="white" />
              <Text style={{
                fontSize: 14,
                fontWeight: '600',
                color: 'white',
                marginLeft: 8,
              }}>
                View All Documents
              </Text>
              <Ionicons name="chevron-forward" size={16} color="white" style={{ marginLeft: 4 }} />
            </TouchableOpacity>
          </View>
        </View>
      </Animated.View>
    );
  }

  function renderReferralSection() {
    return (
      <Animated.View
        style={{
          opacity: fadeAnimation,
          transform: [{ translateY: slideAnimation }],
        }}
      >
        <View style={{
          backgroundColor: 'white',
          marginHorizontal: 20,
          marginTop: 16,
          borderRadius: 20,
          padding: 24,
          shadowColor: '#000',
          shadowOffset: { width: 0, height: 4 },
          shadowOpacity: 0.15,
          shadowRadius: 12,
          elevation: 8,
        }}>
          {/* Header with gradient background */}
          <LinearGradient
            colors={['#ef4444', '#f87171']}
            start={{ x: 0, y: 0 }}
            end={{ x: 1, y: 0 }}
            style={{
              borderRadius: 16,
              padding: 20,
              marginBottom: 20,
            }}
          >
            <View style={{
              flexDirection: 'row',
              alignItems: 'center',
              marginBottom: 8,
            }}>
              <Ionicons name="gift" size={24} color="white" />
              <Text style={{
                fontSize: 20,
                fontWeight: 'bold',
                color: 'white',
                marginLeft: 12,
              }}>
                Referral Program
              </Text>
            </View>
            <Text style={{
              fontSize: 14,
              color: 'rgba(255,255,255,0.9)',
              lineHeight: 20,
            }}>
              Invite friends and earn PKR 500 for each successful referral! 🎉
            </Text>
          </LinearGradient>

          {/* Referral Code Display */}
          <View style={{
            backgroundColor: '#f8fafc',
            borderRadius: 16,
            padding: 20,
            borderWidth: 2,
            borderColor: '#f97316',
            borderStyle: 'dashed',
            marginBottom: 16,
          }}>
            <Text style={{
              fontSize: 14,
              color: '#6b7280',
              marginBottom: 8,
              textAlign: 'center',
              fontWeight: '600',
            }}>
              Your Referral Code
            </Text>

            <TouchableOpacity
              onPress={handleCopyReferralCode}
              style={{
                backgroundColor: 'white',
                borderRadius: 12,
                padding: 16,
                borderWidth: 1,
                borderColor: '#e5e7eb',
                marginBottom: 16,
              }}
            >
              <View style={{
                flexDirection: 'row',
                alignItems: 'center',
                justifyContent: 'center',
              }}>
                <Text style={{
                  fontSize: 24,
                  fontWeight: 'bold',
                  color: '#f97316',
                  letterSpacing: 3,
                  fontFamily: 'monospace',
                  marginRight: 12,
                }}>
                  {mockProfile.riderInfo.riderCode}
                </Text>
                <Ionicons name="copy-outline" size={20} color="#f97316" />
              </View>
              <Text style={{
                fontSize: 12,
                color: '#6b7280',
                textAlign: 'center',
                marginTop: 4,
              }}>
                Tap to copy
              </Text>
            </TouchableOpacity>

            {/* Action Buttons */}
            <View style={{
              flexDirection: 'row',
              gap: 12,
            }}>
              <TouchableOpacity
                onPress={handleCopyReferralCode}
                style={{
                  flex: 1,
                  backgroundColor: '#3b82f6',
                  borderRadius: 12,
                  paddingVertical: 12,
                  flexDirection: 'row',
                  alignItems: 'center',
                  justifyContent: 'center',
                }}
              >
                <Ionicons name="copy-outline" size={16} color="white" />
                <Text style={{
                  fontSize: 14,
                  fontWeight: '600',
                  color: 'white',
                  marginLeft: 6,
                }}>
                  Copy
                </Text>
              </TouchableOpacity>

              <TouchableOpacity
                onPress={handleShareReferralCode}
                style={{
                  flex: 1,
                  backgroundColor: '#f97316',
                  borderRadius: 12,
                  paddingVertical: 12,
                  flexDirection: 'row',
                  alignItems: 'center',
                  justifyContent: 'center',
                }}
              >
                <Ionicons name="share-outline" size={16} color="white" />
                <Text style={{
                  fontSize: 14,
                  fontWeight: '600',
                  color: 'white',
                  marginLeft: 6,
                }}>
                  Share
                </Text>
              </TouchableOpacity>
            </View>
          </View>

          {/* Referral Stats */}
          <View style={{
            flexDirection: 'row',
            backgroundColor: '#f8fafc',
            borderRadius: 12,
            padding: 16,
          }}>
            <View style={{ flex: 1, alignItems: 'center' }}>
              <Text style={{
                fontSize: 20,
                fontWeight: 'bold',
                color: '#f97316',
              }}>
                12
              </Text>
              <Text style={{
                fontSize: 12,
                color: '#6b7280',
                fontWeight: '500',
              }}>
                Invited
              </Text>
            </View>
            <View style={{
              width: 1,
              backgroundColor: '#e5e7eb',
              marginHorizontal: 16,
            }} />
            <View style={{ flex: 1, alignItems: 'center' }}>
              <Text style={{
                fontSize: 20,
                fontWeight: 'bold',
                color: '#10b981',
              }}>
                8
              </Text>
              <Text style={{
                fontSize: 12,
                color: '#6b7280',
                fontWeight: '500',
              }}>
                Joined
              </Text>
            </View>
            <View style={{
              width: 1,
              backgroundColor: '#e5e7eb',
              marginHorizontal: 16,
            }} />
            <View style={{ flex: 1, alignItems: 'center' }}>
              <Text style={{
                fontSize: 20,
                fontWeight: 'bold',
                color: '#3b82f6',
              }}>
                ₨4,000
              </Text>
              <Text style={{
                fontSize: 12,
                color: '#6b7280',
                fontWeight: '500',
              }}>
                Earned
              </Text>
            </View>
          </View>
        </View>
      </Animated.View>
    );
  }

  function renderActionButtons() {
    return (
      <Animated.View
        style={{
          opacity: fadeAnimation,
          transform: [{ translateY: slideAnimation }],
        }}
      >
        <View style={{
          marginHorizontal: 20,
          marginTop: 24,
          gap: 12,
        }}>
          <TouchableOpacity
            onPress={() => navigation.navigate('EditProfile' as never)}
            style={{
              backgroundColor: '#f97316',
              borderRadius: 16,
              padding: 16,
              flexDirection: 'row',
              alignItems: 'center',
              justifyContent: 'center',
              shadowColor: '#f97316',
              shadowOffset: { width: 0, height: 4 },
              shadowOpacity: 0.3,
              shadowRadius: 8,
              elevation: 6,
            }}
          >
            <Ionicons name="pencil" size={20} color="white" />
            <Text style={{
              fontSize: 16,
              fontWeight: '600',
              color: 'white',
              marginLeft: 8,
            }}>
              Edit Profile
            </Text>
          </TouchableOpacity>

          <TouchableOpacity
            onPress={handleLogout}
            style={{
              backgroundColor: 'white',
              borderRadius: 16,
              padding: 16,
              flexDirection: 'row',
              alignItems: 'center',
              justifyContent: 'center',
              borderWidth: 2,
              borderColor: '#ef4444',
              shadowColor: '#000',
              shadowOffset: { width: 0, height: 2 },
              shadowOpacity: 0.1,
              shadowRadius: 8,
              elevation: 3,
            }}
          >
            <Ionicons name="log-out" size={20} color="#ef4444" />
            <Text style={{
              fontSize: 16,
              fontWeight: '600',
              color: '#ef4444',
              marginLeft: 8,
            }}>
              Logout
            </Text>
          </TouchableOpacity>
        </View>
      </Animated.View>
    );
  }
};

export default ProfileMainScreen;
