import React, { useEffect } from 'react';
import { View, Text, Animated } from 'react-native';
import { SafeAreaView } from 'react-native-safe-area-context';

interface SplashScreenProps {
  onFinish: () => void;
}

const SplashScreen: React.FC<SplashScreenProps> = ({ onFinish }) => {
  const fadeAnim = new Animated.Value(0);
  const scaleAnim = new Animated.Value(0.8);

  useEffect(() => {
    // Start animations
    Animated.parallel([
      Animated.timing(fadeAnim, {
        toValue: 1,
        duration: 1000,
        useNativeDriver: true,
      }),
      Animated.spring(scaleAnim, {
        toValue: 1,
        tension: 50,
        friction: 7,
        useNativeDriver: true,
      }),
    ]).start();

    // Auto-finish after 2 seconds
    const timer = setTimeout(() => {
      onFinish();
    }, 2000);

    return () => clearTimeout(timer);
  }, []);

  return (
    <SafeAreaView style={{
      flex: 1,
      backgroundColor: '#f97316',
      justifyContent: 'center',
      alignItems: 'center',
    }}>
      <Animated.View
        style={{
          opacity: fadeAnim,
          transform: [{ scale: scaleAnim }],
          alignItems: 'center',
        }}
      >
        {/* Logo */}
        <View
          style={{
            width: 120,
            height: 120,
            backgroundColor: 'white',
            borderRadius: 60,
            justifyContent: 'center',
            alignItems: 'center',
            marginBottom: 24,
            shadowColor: '#000',
            shadowOffset: { width: 0, height: 4 },
            shadowOpacity: 0.3,
            shadowRadius: 8,
            elevation: 8,
          }}
        >
          <Text style={{
            color: '#f97316',
            fontSize: 36,
            fontWeight: 'bold',
          }}>
            FW
          </Text>
        </View>

        {/* App Name */}
        <Text style={{
          color: 'white',
          fontSize: 32,
          fontWeight: 'bold',
          marginBottom: 8,
        }}>
          FoodWay
        </Text>

        {/* Subtitle */}
        <Text style={{
          color: 'rgba(255, 255, 255, 0.9)',
          fontSize: 18,
          fontWeight: '500',
        }}>
          Rider
        </Text>

        {/* Loading indicator */}
        <View style={{
          marginTop: 40,
          width: 40,
          height: 4,
          backgroundColor: 'rgba(255, 255, 255, 0.3)',
          borderRadius: 2,
          overflow: 'hidden',
        }}>
          <Animated.View
            style={{
              width: '100%',
              height: '100%',
              backgroundColor: 'white',
              borderRadius: 2,
              transform: [{
                translateX: fadeAnim.interpolate({
                  inputRange: [0, 1],
                  outputRange: [-40, 0],
                }),
              }],
            }}
          />
        </View>
      </Animated.View>
    </SafeAreaView>
  );
};

export default SplashScreen;
